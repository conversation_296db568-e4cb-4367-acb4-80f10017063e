import{A as R,B as o,m as u,ak as ee,L as e,C as i,_ as B,P as w,a0 as ae,D as ie,F as j,S as k,al as J,W as K,t as p,K as N,am as O,Q as L,J as te,T as se,M as re,ab as P,b as Q,N as de}from"./SpinnerAugment-DwpTQqCj.js";import{a as ne,I as oe}from"./IconButtonAugment-CpHf993c.js";import{t as le,f as ce}from"./index-McJAeurX.js";import{E as ve}from"./ellipsis-mzD6NhIP.js";const S=(_,{onResize:t,options:g})=>{const v=new ResizeObserver(t);return v.observe(_,g),{destroy(){v.unobserve(_),v.disconnect()}}};var me=j('<div class="c-drawer__hidden-indicator svelte-18f0m3o"><!></div>'),ue=j('<div><div class="c-drawer__left svelte-18f0m3o"><div class="c-drawer__left-content svelte-18f0m3o"><!></div></div> <div aria-hidden="true"></div> <div class="c-drawer__right svelte-18f0m3o"><!></div> <!></div>');function ge(_,t){R(t,!1);let g,v,T=o(t,"initialWidth",8,300),z=o(t,"expandedMinWidth",8,50),X=o(t,"minimizedWidth",8,0),s=o(t,"minimized",12,!1),q=o(t,"class",8,""),G=o(t,"showButton",8,!0),H=o(t,"deadzone",8,0),U=o(t,"columnLayoutThreshold",8,600),d=o(t,"layoutMode",28,()=>{}),W=u(),f=u(),l=u(!1),c=u(T()),y=u(T()),r=u(!1);function C(){s(!s())}function M(){if(e(f)){if(d()!==void 0)return i(r,d()==="column"),void(e(r)&&i(l,!1));i(r,e(f).clientWidth<U()),e(r)&&i(l,!1)}}ee(M),B(()=>(w(s()),w(d())),()=>{s()?(d("row"),i(r,!1)):d()!=="row"||s()||(d(void 0),M())}),B(()=>(w(d()),e(r)),()=>{d()!==void 0&&(i(r,d()==="column"),e(r)&&i(l,!1))}),B(()=>(w(s()),w(X()),e(c)),()=>{i(y,s()?X():e(c))}),ae(),ie();var h=ue();let E;k("mousemove",J,function(a){if(!e(l)||!e(W)||e(r))return;const n=a.clientX-g,$=e(f).clientWidth-200,m=v+n;m<z()?m<z()-H()?s(!0):(i(c,z()),s(!1)):m>$?(i(c,$),s(!1)):(i(c,m),s(!1))}),k("mouseup",J,function(){i(l,!1),i(c,Math.max(e(c),z()))});var b=p(h),D=p(b);K(D,"",{},{width:"var(--augment-drawer-width)","min-width":"var(--augment-drawer-width)","max-width":"var(--augment-drawer-width)"});var V=p(D);N(V,t,"left",{},null),O(b,a=>i(W,a),()=>e(W));var x=L(b,2);let F;var I=L(x,2),Y=p(I);N(Y,t,"right",{},null);var Z=L(I,2),A=a=>{var n=me(),$=p(n);oe($,{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$events:{click:C},children:(m,fe)=>{ve(m,{})},$$slots:{default:!0}}),le(3,n,()=>ce,()=>({y:0,x:0,duration:200})),Q(a,n)};te(Z,a=>{s()&&G()&&a(A)}),O(h,a=>i(f,a),()=>e(f)),ne(h,(a,n)=>S==null?void 0:S(a,n),()=>({onResize:()=>d()===void 0&&M()})),se((a,n)=>{E=P(h,1,`c-drawer ${q()??""}`,"svelte-18f0m3o",E,a),K(b,`--augment-drawer-width:${e(y)??""}px;`),D.inert=e(l),F=P(x,1,"c-drawer__handle svelte-18f0m3o",null,F,n)},[()=>({"is-dragging":e(l),"is-hidden":!e(y),"is-column":e(r)}),()=>({"is-locked":e(r)})],re),k("mousedown",x,function(a){e(r)||(i(l,!0),g=a.clientX,v=e(W).offsetWidth,a.preventDefault())}),k("dblclick",x,C),Q(_,h),de()}export{ge as D,S as r};
