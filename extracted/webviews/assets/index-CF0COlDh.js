var cl=Object.defineProperty;var dl=(e,t,n)=>t in e?cl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var g=(e,t,n)=>dl(e,typeof t!="symbol"?t+"":t,n);import{a as hl}from"./async-messaging-3euWO_Vj.js";import{W as E,S as pl,a as Ms,b as Le,B as ml,h as gl}from"./IconButtonAugment-CpHf993c.js";import{R as ne,P as z,C as R,b as zn,I as Zt,a as K,E as fl}from"./message-broker-CJi4HhV6.js";import{C as yl}from"./types-CGlLNakm.js";import{n as _l,f as bl,i as vl,F as Gr}from"./focusTrapStack-CKc3PkF0.js";import{w as Ht,y as _r,aA as Sl,f as Po,b as O,aH as Os,A as _e,B as x,v as Do,u as oe,_ as Ae,a0 as gt,D as be,S as we,al as El,F as re,K as se,t as ue,T as ft,ab as yt,a4 as ke,N as ve,a3 as Be,L as F,m as Re,P as Ke,a5 as Tl,C as Me,z as Lo,l as W,a7 as et,I as Ce,J as Mt,G as tt,Q as Ot,X as Fo,H as Ge,aF as Vr,M as wn,ba as jr,$ as Uo,a as $o,b0 as Yr,am as qo,aD as wl,O as Il,V as Nl}from"./SpinnerAugment-DwpTQqCj.js";import{a as kl,c as Cl,_ as xl,i as br}from"./isObjectLike-BdluLIvS.js";import{c as Al,e as hn,f as Rl,C as Ml,a as pn,R as Ol,b as ot,g as Kr}from"./CardAugment-C6Oa90En.js";import{B as Pl,b as Dl}from"./BaseTextInput-D9ZtAsmP.js";function vr(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var _;function Ll(){let e=0,t=0;for(let s=0;s<28;s+=7){let r=this.buf[this.pos++];if(e|=(127&r)<<s,!(128&r))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let s=3;s<=31;s+=7){let r=this.buf[this.pos++];if(t|=(127&r)<<s,!(128&r))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function Xn(e,t,n){for(let a=0;a<28;a+=7){const o=e>>>a,i=!(!(o>>>7)&&t==0),c=255&(i?128|o:o);if(n.push(c),!i)return}const s=e>>>28&15|(7&t)<<4,r=!!(t>>3);if(n.push(255&(r?128|s:s)),r){for(let a=3;a<31;a+=7){const o=t>>>a,i=!!(o>>>7),c=255&(i?128|o:o);if(n.push(c),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(_||(_={}));const mn=4294967296;function Wr(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let s=0,r=0;function a(o,i){const c=Number(e.slice(o,i));r*=n,s=s*n+c,s>=mn&&(r+=s/mn|0,s%=mn)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),t?Ho(s,r):Sr(s,r)}function zr(e,t){if({lo:e,hi:t}=function(c,l){return{lo:c>>>0,hi:l>>>0}}(e,t),t<=2097151)return String(mn*t+e);const n=16777215&(e>>>24|t<<8),s=t>>16&65535;let r=(16777215&e)+6777216*n+6710656*s,a=n+8147497*s,o=2*s;const i=1e7;return r>=i&&(a+=Math.floor(r/i),r%=i),a>=i&&(o+=Math.floor(a/i),a%=i),o.toString()+Xr(a)+Xr(r)}function Sr(e,t){return{lo:0|e,hi:0|t}}function Ho(e,t){return t=~t,e?e=1+~e:t+=1,Sr(e,t)}const Xr=e=>{const t=String(e);return"0000000".slice(t.length)+t};function Jr(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function Fl(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var Zr={};const L=Ul();function Ul(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof Zr!="object"||Zr.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),s=BigInt("0"),r=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>n||o<t)throw new Error(`invalid int64: ${a}`);return o},uParse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>r||o<s)throw new Error(`invalid uint64: ${a}`);return o},enc(a){return e.setBigInt64(0,this.parse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(a){return e.setBigInt64(0,this.uParse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(a,o)=>(e.setInt32(0,a,!0),e.setInt32(4,o,!0),e.getBigInt64(0,!0)),uDec:(a,o)=>(e.setInt32(0,a,!0),e.setInt32(4,o,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),Qr(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),ea(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),Qr(t),Wr(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),ea(t),Wr(t)),dec:(t,n)=>function(s,r){let a=Sr(s,r);const o=2147483648&a.hi;o&&(a=Ho(a.lo,a.hi));const i=zr(a.lo,a.hi);return o?"-"+i:i}(t,n),uDec:(t,n)=>zr(t,n)}}function Qr(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function ea(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function nt(e,t){switch(e){case _.STRING:return"";case _.BOOL:return!1;case _.DOUBLE:case _.FLOAT:return 0;case _.INT64:case _.UINT64:case _.SFIXED64:case _.FIXED64:case _.SINT64:return t?"0":L.zero;case _.BYTES:return new Uint8Array(0);default:return 0}}const $e=Symbol.for("reflect unsafe local");function Bo(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(s=>s.localName===n)}function $l(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(s,r){switch(s){case _.BOOL:return r===!1;case _.STRING:return r==="";case _.BYTES:return r instanceof Uint8Array&&!r.byteLength;default:return r==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function Pt(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function Go(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function Vo(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function Xe(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function Er(e,t){var n,s,r,a;if(Xe(e)&&$e in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.listKind==i.listKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((r=o.enum)===null||r===void 0?void 0:r.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function Tr(e,t){var n,s,r,a;if(Xe(e)&&$e in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.mapKey===i.mapKey&&o.mapKind==i.mapKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((s=i.message)===null||s===void 0?void 0:s.typeName)&&((r=o.enum)===null||r===void 0?void 0:r.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function wr(e,t){return Xe(e)&&$e in e&&"desc"in e&&Xe(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function Wt(e){const t=e.fields[0];return jo(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function jo(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const ql=999,Hl=998,Qt=2;function Ee(e,t){if(vr(t,e))return t;const n=function(s){let r;if(function(a){switch(a.file.edition){case ql:return!1;case Hl:return!0;default:return a.fields.some(o=>o.presence!=Qt&&o.fieldKind!="message"&&!o.oneof)}}(s)){const a=na.get(s);let o,i;if(a)({prototype:o,members:i}=a);else{o={},i=new Set;for(const c of s.members)c.kind!="oneof"&&(c.fieldKind!="scalar"&&c.fieldKind!="enum"||c.presence!=Qt&&(i.add(c),o[c.localName]=Jn(c)));na.set(s,{prototype:o,members:i})}r=Object.create(o),r.$typeName=s.typeName;for(const c of s.members)if(!i.has(c)){if(c.kind=="field"&&(c.fieldKind=="message"||(c.fieldKind=="scalar"||c.fieldKind=="enum")&&c.presence!=Qt))continue;r[c.localName]=Jn(c)}}else{r={$typeName:s.typeName};for(const a of s.members)a.kind!="oneof"&&a.presence!=Qt||(r[a.localName]=Jn(a))}return r}(e);return t!==void 0&&function(s,r,a){for(const o of s.members){let i,c=a[o.localName];if(c!=null){if(o.kind=="oneof"){const l=Bo(a,o);if(!l)continue;i=l,c=Go(a,l)}else i=o;switch(i.fieldKind){case"message":c=Ir(i,c);break;case"scalar":c=Yo(i,c);break;case"list":c=Gl(i,c);break;case"map":c=Bl(i,c)}Vo(r,i,c)}}}(e,n,t),n}function Yo(e,t){return e.scalar==_.BYTES?Nr(t):t}function Bl(e,t){if(Xe(t)){if(e.scalar==_.BYTES)return ta(t,Nr);if(e.mapKind=="message")return ta(t,n=>Ir(e,n))}return t}function Gl(e,t){if(Array.isArray(t)){if(e.scalar==_.BYTES)return t.map(Nr);if(e.listKind=="message")return t.map(n=>Ir(e,n))}return t}function Ir(e,t){if(e.fieldKind=="message"&&!e.oneof&&Wt(e.message))return Yo(e.message.fields[0],t);if(Xe(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!vr(t,e.message))return Ee(e.message,t)}return t}function Nr(e){return Array.isArray(e)?new Uint8Array(e):e}function ta(e,t){const n={};for(const s of Object.entries(e))n[s[0]]=t(s[1]);return n}const Vl=Symbol(),na=new WeakMap;function Jn(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return Vl;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?nt(e.scalar,e.longAsString):e.enum.values[0].number}const jl=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class ie extends Error{constructor(t,n,s="FieldValueInvalidError"){super(n),this.name=s,this.field=()=>t}}const Zn=Symbol.for("@bufbuild/protobuf/text-encoding");function kr(){if(globalThis[Zn]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[Zn]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[Zn]}var $;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})($||($={}));const Ko=34028234663852886e22,Wo=-34028234663852886e22,zo=4294967295,Xo=2147483647,Jo=-2147483648;class Zo{constructor(t=kr().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let r=0;r<this.chunks.length;r++)t+=this.chunks[r].length;let n=new Uint8Array(t),s=0;for(let r=0;r<this.chunks.length;r++)n.set(this.chunks[r],s),s+=this.chunks[r].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(sa(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return Qn(t),Jr(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(s){if(typeof s=="string"){const r=s;if(s=Number(s),Number.isNaN(s)&&r!=="NaN")throw new Error("invalid float32: "+r)}else if(typeof s!="number")throw new Error("invalid float32: "+typeof s);if(Number.isFinite(s)&&(s>Ko||s<Wo))throw new Error("invalid float32: "+s)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){sa(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){Qn(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return Qn(t),Jr(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),r=L.enc(t);return s.setInt32(0,r.lo,!0),s.setInt32(4,r.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),s=new DataView(n.buffer),r=L.uEnc(t);return s.setInt32(0,r.lo,!0),s.setInt32(4,r.hi,!0),this.raw(n)}int64(t){let n=L.enc(t);return Xn(n.lo,n.hi,this.buf),this}sint64(t){const n=L.enc(t),s=n.hi>>31;return Xn(n.lo<<1^s,(n.hi<<1|n.lo>>>31)^s,this.buf),this}uint64(t){const n=L.uEnc(t);return Xn(n.lo,n.hi,this.buf),this}}class Cr{constructor(t,n=kr().decodeUtf8){this.decodeUtf8=n,this.varint64=Ll,this.uint32=Fl,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,s=7&t;if(n<=0||s<0||s>5)throw new Error("illegal tag: field no "+n+" wire type "+s);return[n,s]}skip(t,n){let s=this.pos;switch(t){case $.Varint:for(;128&this.buf[this.pos++];);break;case $.Bit64:this.pos+=4;case $.Bit32:this.pos+=4;break;case $.LengthDelimited:let r=this.uint32();this.pos+=r;break;case $.StartGroup:for(;;){const[a,o]=this.tag();if(o===$.EndGroup){if(n!==void 0&&a!==n)throw new Error("invalid end group tag");break}this.skip(o,a)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(s,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return L.dec(...this.varint64())}uint64(){return L.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),s=-(1&t);return t=(t>>>1|(1&n)<<31)^s,n=n>>>1^s,L.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return L.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return L.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function Qn(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>Xo||e<Jo)throw new Error("invalid int32: "+e)}function sa(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>zo||e<0)throw new Error("invalid uint32: "+e)}function Je(e,t){const n=e.fieldKind=="list"?Er(t,e):e.fieldKind=="map"?Tr(t,e):xr(e,t);if(n===!0)return;let s;switch(e.fieldKind){case"list":s=`expected ${ti(e)}, got ${G(t)}`;break;case"map":s=`expected ${ni(e)}, got ${G(t)}`;break;default:s=In(e,t,n)}return new ie(e,s)}function ra(e,t,n){const s=xr(e,n);if(s!==!0)return new ie(e,`list item #${t+1}: ${In(e,n,s)}`)}function xr(e,t){return e.scalar!==void 0?Qo(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):wr(t,e.message)}function Qo(e,t){switch(t){case _.DOUBLE:return typeof e=="number";case _.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>Ko||e<Wo)||`${e.toFixed()} out of range`);case _.INT32:case _.SFIXED32:case _.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>Xo||e<Jo)||`${e.toFixed()} out of range`);case _.FIXED32:case _.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>zo||e<0)||`${e.toFixed()} out of range`);case _.BOOL:return typeof e=="boolean";case _.STRING:return typeof e=="string"&&(kr().checkUtf8(e)||"invalid UTF8");case _.BYTES:return e instanceof Uint8Array;case _.INT64:case _.SFIXED64:case _.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return L.parse(e),!0}catch{return`${e} out of range`}return!1;case _.FIXED64:case _.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return L.uParse(e),!0}catch{return`${e} out of range`}return!1}}function In(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${G(t)}`,e.scalar!==void 0?`expected ${function(s){switch(s){case _.STRING:return"string";case _.BOOL:return"boolean";case _.INT64:case _.SINT64:case _.SFIXED64:return"bigint (int64)";case _.UINT64:case _.FIXED64:return"bigint (uint64)";case _.BYTES:return"Uint8Array";case _.DOUBLE:return"number (float64)";case _.FLOAT:return"number (float32)";case _.FIXED32:case _.UINT32:return"number (uint32)";case _.INT32:case _.SFIXED32:case _.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${ei(e.message)}`+n}function G(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:Er(e)?ti(e.field()):Tr(e)?ni(e.field()):wr(e)?ei(e.desc):vr(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function ei(e){return`ReflectMessage (${e.typeName})`}function ti(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${_[e.scalar]})`}}function ni(e){switch(e.mapKind){case"message":return`ReflectMap (${_[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${_[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${_[e.mapKey]}, ${_[e.scalar]})`}}function me(e,t,n=!0){return new si(e,t,n)}class si{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,s)=>n.number-s.number)}constructor(t,n,s=!0){this.lists=new Map,this.maps=new Map,this.check=s,this.desc=t,this.message=this[$e]=n??Ee(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return Et(this.message,t),Bo(this.message,t)}isSet(t){return Et(this.message,t),$l(this.message,t)}clear(t){Et(this.message,t),function(n,s){const r=s.localName;if(s.oneof){const a=s.oneof.localName;n[a].case===r&&(n[a]={case:void 0})}else if(s.presence!=2)delete n[r];else switch(s.fieldKind){case"map":n[r]={};break;case"list":n[r]=[];break;case"enum":n[r]=s.enum.values[0].number;break;case"scalar":n[r]=nt(s.scalar,s.longAsString)}}(this.message,t)}get(t){Et(this.message,t);const n=Go(this.message,t);switch(t.fieldKind){case"list":let s=this.lists.get(t);return s&&s[$e]===n||this.lists.set(t,s=new Yl(t,n,this.check)),s;case"map":let r=this.maps.get(t);return r&&r[$e]===n||this.maps.set(t,r=new Kl(t,n,this.check)),r;case"message":return Rr(t,n,this.check);case"scalar":return n===void 0?nt(t.scalar,!1):Mr(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(Et(this.message,t),this.check){const r=Je(t,n);if(r)throw r}let s;s=t.fieldKind=="message"?Ar(t,n):Tr(n)||Er(n)?n[$e]:Or(t,n),Vo(this.message,t,s)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function Et(e,t){if(t.parent.typeName!==e.$typeName)throw new ie(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class Yl{field(){return this._field}get size(){return this._arr.length}constructor(t,n,s){this._field=t,this._arr=this[$e]=n,this.check=s}get(t){const n=this._arr[t];return n===void 0?void 0:es(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new ie(this._field,`list item #${t+1}: out of range`);if(this.check){const s=ra(this._field,t,n);if(s)throw s}this._arr[t]=aa(this._field,n)}add(t){if(this.check){const n=ra(this._field,this._arr.length,t);if(n)throw n}this._arr.push(aa(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield es(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,es(this._field,this._arr[t],this.check)]}}class Kl{constructor(t,n,s=!0){this.obj=this[$e]=n??{},this.check=s,this._field=t}field(){return this._field}set(t,n){if(this.check){const s=function(r,a,o){const i=Qo(a,r.mapKey);if(i!==!0)return new ie(r,`invalid map key: ${In({scalar:r.mapKey},a,i)}`);const c=xr(r,o);return c!==!0?new ie(r,`map entry ${G(a)}: ${In(r,o,c)}`):void 0}(this._field,t,n);if(s)throw s}return this.obj[en(t)]=function(s,r){return s.mapKind=="message"?Ar(s,r):Or(s,r)}(this._field,n),this}delete(t){const n=en(t),s=Object.prototype.hasOwnProperty.call(this.obj,n);return s&&delete this.obj[n],s}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[en(t)];return n!==void 0&&(n=ts(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,en(t))}*keys(){for(const t of Object.keys(this.obj))yield oa(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[oa(t[0],this._field.mapKey),ts(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield ts(this._field,t,this.check)}forEach(t,n){for(const s of this.entries())t.call(n,s[1],s[0],this)}}function Ar(e,t){return wr(t)?jo(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?ai(t.message):t.message:t}function Rr(e,t,n){return t!==void 0&&(Wt(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:Mr(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&Xe(t)&&(t=ri(t))),new si(e.message,t,n)}function aa(e,t){return e.listKind=="message"?Ar(e,t):Or(e,t)}function es(e,t,n){return e.listKind=="message"?Rr(e,t,n):Mr(e,t)}function ts(e,t,n){return e.mapKind=="message"?Rr(e,t,n):t}function en(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function oa(e,t){switch(t){case _.STRING:return e;case _.INT32:case _.FIXED32:case _.UINT32:case _.SFIXED32:case _.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case _.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case _.UINT64:case _.FIXED64:try{return L.uParse(e)}catch{}break;default:try{return L.parse(e)}catch{}}return e}function Mr(e,t){switch(e.scalar){case _.INT64:case _.SFIXED64:case _.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=L.parse(t));break;case _.FIXED64:case _.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=L.uParse(t))}return t}function Or(e,t){switch(e.scalar){case _.INT64:case _.SFIXED64:case _.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=L.parse(t));break;case _.FIXED64:case _.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=L.uParse(t))}return t}function ri(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(Xe(e))for(const[n,s]of Object.entries(e))t.fields[n]=ii(s);return t}function ai(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=oi(s);return t}function oi(e){switch(e.kind.case){case"structValue":return ai(e.kind.value);case"listValue":return e.kind.value.values.map(oi);case"nullValue":case void 0:return null;default:return e.kind.value}}function ii(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const s of e)n.values.push(ii(s));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:ri(e)}}return t}function li(e){const t=function(){if(!it){it=[];const c=ui("std");for(let l=0;l<c.length;l++)it[c[l].charCodeAt(0)]=l;it[45]=c.indexOf("+"),it[95]=c.indexOf("/")}return it}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let s,r=new Uint8Array(n),a=0,o=0,i=0;for(let c=0;c<e.length;c++){if(s=t[e.charCodeAt(c)],s===void 0)switch(e[c]){case"=":o=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(o){case 0:i=s,o=1;break;case 1:r[a++]=i<<2|(48&s)>>4,i=s,o=2;break;case 2:r[a++]=(15&i)<<4|(60&s)>>2,i=s,o=3;break;case 3:r[a++]=(3&i)<<6|s,o=0}}if(o==1)throw Error("invalid base64 string");return r.subarray(0,a)}let tn,ia,it;function ui(e){return tn||(tn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),ia=tn.slice(0,-2).concat("-","_")),e=="url"?ia:tn}function Bt(e){let t=!1;const n=[];for(let s=0;s<e.length;s++){let r=e.charAt(s);switch(r){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(r),t=!1;break;default:t&&(t=!1,r=r.toUpperCase()),n.push(r)}}return n.join("")}const Wl=new Set(["constructor","toString","toJSON","valueOf"]);function Gt(e){return Wl.has(e)?e+"$":e}function Pr(e){for(const t of e.field)Pt(t,"jsonName")||(t.jsonName=Bt(t.name));e.nestedType.forEach(Pr)}function zl(e,t){switch(e){case _.STRING:return t;case _.BYTES:{const n=function(s){const r=[],a={tail:s,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(o){if(this.tail.length>=o){const i=this.tail.substring(0,o);return this.tail=this.tail.substring(o),i}return!1}};for(;a.next();)if(a.c==="\\"){if(a.next())switch(a.c){case"\\":r.push(a.c.charCodeAt(0));break;case"b":r.push(8);break;case"f":r.push(12);break;case"n":r.push(10);break;case"r":r.push(13);break;case"t":r.push(9);break;case"v":r.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const o=a.c,i=a.take(2);if(i===!1)return!1;const c=parseInt(o+i,8);if(Number.isNaN(c))return!1;r.push(c);break}case"x":{const o=a.c,i=a.take(2);if(i===!1)return!1;const c=parseInt(o+i,16);if(Number.isNaN(c))return!1;r.push(c);break}case"u":{const o=a.c,i=a.take(4);if(i===!1)return!1;const c=parseInt(o+i,16);if(Number.isNaN(c))return!1;const l=new Uint8Array(4);new DataView(l.buffer).setInt32(0,c,!0),r.push(l[0],l[1],l[2],l[3]);break}case"U":{const o=a.c,i=a.take(8);if(i===!1)return!1;const c=L.uEnc(o+i),l=new Uint8Array(8),d=new DataView(l.buffer);d.setInt32(0,c.lo,!0),d.setInt32(4,c.hi,!0),r.push(l[0],l[1],l[2],l[3],l[4],l[5],l[6],l[7]);break}}}else r.push(a.c.charCodeAt(0));return new Uint8Array(r)}(t);if(n===!1)throw new Error(`cannot parse ${_[e]} default value: ${t}`);return n}case _.INT64:case _.SFIXED64:case _.SINT64:return L.parse(t);case _.UINT64:case _.FIXED64:return L.uParse(t);case _.DOUBLE:case _.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case _.BOOL:return t==="true";case _.INT32:case _.UINT32:case _.SINT32:case _.FIXED32:case _.SFIXED32:return parseInt(t,10)}}function*Ps(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*Ps(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*Ps(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function ci(...e){const t=function(){const n=new Map,s=new Map,r=new Map;return{kind:"registry",types:n,extendees:s,[Symbol.iterator]:()=>n.values(),get files(){return r.values()},addFile(a,o,i){if(r.set(a.proto.name,a),!o)for(const c of Ps(a))this.add(c);if(i)for(const c of a.dependencies)this.addFile(c,o,i)},add(a){if(a.kind=="extension"){let o=s.get(a.extendee.typeName);o||s.set(a.extendee.typeName,o=new Map),o.set(a.number,a)}n.set(a.typeName,a)},get:a=>n.get(a),getFile:a=>r.get(a),getMessage(a){const o=n.get(a);return(o==null?void 0:o.kind)=="message"?o:void 0},getEnum(a){const o=n.get(a);return(o==null?void 0:o.kind)=="enum"?o:void 0},getExtension(a){const o=n.get(a);return(o==null?void 0:o.kind)=="extension"?o:void 0},getExtensionFor(a,o){var i;return(i=s.get(a.typeName))===null||i===void 0?void 0:i.get(o)},getService(a){const o=n.get(a);return(o==null?void 0:o.kind)=="service"?o:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)ca(n,t);return t}if("$typeName"in e[0]){let a=function(o){const i=[];for(const c of o.dependency){if(t.getFile(c)!=null||r.has(c))continue;const l=s(c);if(!l)throw new Error(`Unable to resolve ${c}, imported by ${o.name}`);"kind"in l?t.addFile(l,!1,!0):(r.add(l.name),i.push(l))}return i.concat(...i.map(a))};const n=e[0],s=e[1],r=new Set;for(const o of[n,...a(n)].reverse())ca(o,t)}else for(const n of e)for(const s of n.files)t.addFile(s);return t}const Xl=998,Jl=999,Zl=9,gn=10,Ct=11,Ql=12,la=14,Ds=3,eu=2,ua=1,tu=0,nu=1,su=2,ru=3,au=1,ou=2,iu=1,di={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function ca(e,t){var n,s;const r={kind:"file",proto:e,deprecated:(s=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&s!==void 0&&s,edition:cu(e),name:e.name.replace(/\.proto$/,""),dependencies:du(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},a=new Map,o={get:i=>a.get(i),add(i){var c;Se(((c=i.proto.options)===null||c===void 0?void 0:c.mapEntry)===!0),a.set(i.typeName,i)}};for(const i of e.enumType)hi(i,r,void 0,t);for(const i of e.messageType)pi(i,r,void 0,t,o);for(const i of e.service)lu(i,r,t);Ls(r,t);for(const i of a.values())Fs(i,t,o);for(const i of r.messages)Fs(i,t,o),Ls(i,t);t.addFile(r,!0)}function Ls(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const s=Us(n,e,t);e.extensions.push(s),t.add(s)}break;case"message":for(const n of e.proto.extension){const s=Us(n,e,t);e.nestedExtensions.push(s),t.add(s)}for(const n of e.nestedMessages)Ls(n,t)}}function Fs(e,t,n){const s=e.proto.oneofDecl.map(a=>function(o,i){return{kind:"oneof",proto:o,deprecated:!1,parent:i,fields:[],name:o.name,localName:Gt(Bt(o.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(a,e)),r=new Set;for(const a of e.proto.field){const o=hu(a,s),i=Us(a,e,t,o,n);e.fields.push(i),e.field[i.localName]=i,o===void 0?e.members.push(i):(o.fields.push(i),r.has(o)||(r.add(o),e.members.push(o)))}for(const a of s.filter(o=>r.has(o)))e.oneofs.push(a);for(const a of e.nestedMessages)Fs(a,t,n)}function hi(e,t,n,s){var r,a,o,i,c;const l=function(u,h){const p=(m=u,(m.substring(0,1)+m.substring(1).replace(/[A-Z]/g,y=>"_"+y)).toLowerCase()+"_");var m;for(const y of h){if(!y.name.toLowerCase().startsWith(p))return;const f=y.name.substring(p.length);if(f.length==0||/^\d/.test(f))return}return p}(e.name,e.value),d={kind:"enum",proto:e,deprecated:(a=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&a!==void 0&&a,file:t,parent:n,open:!0,name:e.name,typeName:Vn(e,n,t),value:{},values:[],sharedPrefix:l,toString(){return`enum ${this.typeName}`}};d.open=function(u){var h;return iu==bt("enumType",{proto:u.proto,parent:(h=u.parent)!==null&&h!==void 0?h:u.file})}(d),s.add(d);for(const u of e.value){const h=u.name;d.values.push(d.value[u.number]={kind:"enum_value",proto:u,deprecated:(i=(o=e.options)===null||o===void 0?void 0:o.deprecated)!==null&&i!==void 0&&i,parent:d,name:h,localName:Gt(l==null?h:h.substring(l.length)),number:u.number,toString:()=>`enum value ${d.typeName}.${h}`})}((c=n==null?void 0:n.nestedEnums)!==null&&c!==void 0?c:t.enums).push(d)}function pi(e,t,n,s,r){var a,o,i,c;const l={kind:"message",proto:e,deprecated:(o=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,file:t,parent:n,name:e.name,typeName:Vn(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?r.add(l):(((c=n==null?void 0:n.nestedMessages)!==null&&c!==void 0?c:t.messages).push(l),s.add(l));for(const d of e.enumType)hi(d,t,l,s);for(const d of e.nestedType)pi(d,t,l,s,r)}function lu(e,t,n){var s,r;const a={kind:"service",proto:e,deprecated:(r=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&r!==void 0&&r,file:t,name:e.name,typeName:Vn(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(a),n.add(a);for(const o of e.method){const i=uu(o,a,n);a.methods.push(i),a.method[i.localName]=i}}function uu(e,t,n){var s,r,a,o;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const c=n.getMessage(Ue(e.inputType)),l=n.getMessage(Ue(e.outputType));Se(c,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),Se(l,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const d=e.name;return{kind:"rpc",proto:e,deprecated:(r=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&r!==void 0&&r,parent:t,name:d,localName:Gt(d.length?Gt(d[0].toLowerCase()+d.substring(1)):d),methodKind:i,input:c,output:l,idempotency:(o=(a=e.options)===null||a===void 0?void 0:a.idempotencyLevel)!==null&&o!==void 0?o:tu,toString:()=>`rpc ${t.typeName}.${d}`}}function Us(e,t,n,s,r){var a,o,i;const c=r===void 0,l={kind:"field",proto:e,deprecated:(o=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:pu(e,s,c,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(c){const p=t.kind=="file"?t:t.file,m=t.kind=="file"?void 0:t,y=Vn(e,m,p);l.kind="extension",l.file=p,l.parent=m,l.oneof=void 0,l.typeName=y,l.jsonName=`[${y}]`,l.toString=()=>`extension ${y}`;const f=n.getMessage(Ue(e.extendee));Se(f,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),l.extendee=f}else{const p=t;Se(p.kind=="message"),l.parent=p,l.oneof=s,l.localName=s?Bt(e.name):Gt(Bt(e.name)),l.jsonName=e.jsonName,l.toString=()=>`field ${p.typeName}.${e.name}`}const d=e.label,u=e.type,h=(i=e.options)===null||i===void 0?void 0:i.jstype;if(d===Ds){const p=u==Ct?r==null?void 0:r.get(Ue(e.typeName)):void 0;if(p){l.fieldKind="map";const{key:m,value:y}=function(f){const b=f.fields.find(v=>v.number===1),S=f.fields.find(v=>v.number===2);return Se(b&&b.fieldKind=="scalar"&&b.scalar!=_.BYTES&&b.scalar!=_.FLOAT&&b.scalar!=_.DOUBLE&&S&&S.fieldKind!="list"&&S.fieldKind!="map"),{key:b,value:S}}(p);return l.mapKey=m.scalar,l.mapKind=y.fieldKind,l.message=y.message,l.delimitedEncoding=!1,l.enum=y.enum,l.scalar=y.scalar,l}switch(l.fieldKind="list",u){case Ct:case gn:l.listKind="message",l.message=n.getMessage(Ue(e.typeName)),Se(l.message),l.delimitedEncoding=da(e,t);break;case la:l.listKind="enum",l.enum=n.getEnum(Ue(e.typeName)),Se(l.enum);break;default:l.listKind="scalar",l.scalar=u,l.longAsString=h==ua}return l.packed=function(m,y){if(m.label!=Ds)return!1;switch(m.type){case Zl:case Ql:case gn:case Ct:return!1}const f=m.options;return f&&Pt(f,"packed")?f.packed:au==bt("repeatedFieldEncoding",{proto:m,parent:y})}(e,t),l}switch(u){case Ct:case gn:l.fieldKind="message",l.message=n.getMessage(Ue(e.typeName)),Se(l.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.delimitedEncoding=da(e,t),l.getDefaultValue=()=>{};break;case la:{const p=n.getEnum(Ue(e.typeName));Se(p!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.fieldKind="enum",l.enum=n.getEnum(Ue(e.typeName)),l.getDefaultValue=()=>Pt(e,"defaultValue")?function(m,y){const f=m.values.find(b=>b.name===y);if(!f)throw new Error(`cannot parse ${m} default value: ${y}`);return f.number}(p,e.defaultValue):void 0;break}default:l.fieldKind="scalar",l.scalar=u,l.longAsString=h==ua,l.getDefaultValue=()=>Pt(e,"defaultValue")?zl(u,e.defaultValue):void 0}return l}function cu(e){switch(e.syntax){case"":case"proto2":return Xl;case"proto3":return Jl;case"editions":if(e.edition in di)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function du(e,t){return e.dependency.map(n=>{const s=t.getFile(n);if(!s)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return s})}function Vn(e,t,n){let s;return s=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,s}function Ue(e){return e.startsWith(".")?e.substring(1):e}function hu(e,t){if(!Pt(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return Se(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function pu(e,t,n,s){return e.label==eu?ru:e.label==Ds?su:t||e.proto3Optional||e.type==Ct||n?nu:bt("fieldPresence",{proto:e,parent:s})}function da(e,t){return e.type==gn||ou==bt("messageEncoding",{proto:e,parent:t})}function bt(e,t){var n,s;const r=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(r){const a=r[e];if(a!=0)return a}if("kind"in t){if(t.kind=="message")return bt(e,(s=t.parent)!==null&&s!==void 0?s:t.file);const a=di[t.edition];if(!a)throw new Error(`feature default for edition ${t.edition} not found`);return a[e]}return bt(e,t.parent)}function Se(e,t){if(!e)throw new Error(t)}function mu(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(mi),enumType:n.enumType.map(gi)}))}(e);return t.messageType.forEach(Pr),ci(t,()=>{}).getFile(t.name)}function mi(e){var t,n,s,r,a,o,i,c;return{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(gu))!==null&&n!==void 0?n:[],extension:[],nestedType:(r=(s=e.nestedType)===null||s===void 0?void 0:s.map(mi))!==null&&r!==void 0?r:[],enumType:(o=(a=e.enumType)===null||a===void 0?void 0:a.map(gi))!==null&&o!==void 0?o:[],extensionRange:(c=(i=e.extensionRange)===null||i===void 0?void 0:i.map(l=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},l)))!==null&&c!==void 0?c:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function gu(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?fu(e.options):void 0}))}function fu(e){var t,n,s;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(s=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(a=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},a)))!==null&&s!==void 0?s:[],uninterpretedOption:[]}))}function gi(e){return{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(t=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},t))}}function zt(e,t,...n){return n.reduce((s,r)=>s.nestedMessages[r],e.messages[t])}const yu=zt(mu({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var ha,pa,ma,ga,fa,ya,_a,ba,va,Sa,Ea,Ta,wa,Ia,Na,ka,Ca,xa;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(ha||(ha={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(pa||(pa={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(ma||(ma={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(ga||(ga={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(fa||(fa={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(ya||(ya={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(_a||(_a={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(ba||(ba={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(va||(va={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(Sa||(Sa={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(Ea||(Ea={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(Ta||(Ta={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(wa||(wa={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(Ia||(Ia={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(Na||(Na={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(ka||(ka={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(Ca||(Ca={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(xa||(xa={}));const _u={readUnknownFields:!0};function Dr(e,t,n){const s=me(e,void 0,!1);return fi(s,new Cr(t),_u,!1,t.byteLength),s.message}function fi(e,t,n,s,r){var a;const o=s?t.len:t.pos+r;let i,c;const l=(a=e.getUnknown())!==null&&a!==void 0?a:[];for(;t.pos<o&&([i,c]=t.tag(),!s||c!=$.EndGroup);){const d=e.findNumber(i);if(d)yi(e,t,d,c,n);else{const u=t.skip(c,i);n.readUnknownFields&&l.push({no:i,wireType:c,data:u})}}if(s&&(c!=$.EndGroup||i!==r))throw new Error("invalid end group tag");l.length>0&&e.setUnknown(l)}function yi(e,t,n,s,r){switch(n.fieldKind){case"scalar":e.set(n,lt(t,n.scalar));break;case"enum":e.set(n,lt(t,_.INT32));break;case"message":e.set(n,ns(t,r,n,e.get(n)));break;case"list":(function(a,o,i,c){var l;const d=i.field();if(d.listKind==="message")return void i.add(ns(a,c,d));const u=(l=d.scalar)!==null&&l!==void 0?l:_.INT32;if(!(o==$.LengthDelimited&&u!=_.STRING&&u!=_.BYTES))return void i.add(lt(a,u));const p=a.uint32()+a.pos;for(;a.pos<p;)i.add(lt(a,u))})(t,s,e.get(n),r);break;case"map":(function(a,o,i){const c=o.field();let l,d;const u=a.pos+a.uint32();for(;a.pos<u;){const[h]=a.tag();switch(h){case 1:l=lt(a,c.mapKey);break;case 2:switch(c.mapKind){case"scalar":d=lt(a,c.scalar);break;case"enum":d=a.int32();break;case"message":d=ns(a,i,c)}}}if(l===void 0&&(l=nt(c.mapKey,!1)),d===void 0)switch(c.mapKind){case"scalar":d=nt(c.scalar,!1);break;case"enum":d=c.enum.values[0].number;break;case"message":d=me(c.message,void 0,!1)}o.set(l,d)})(t,e.get(n),r)}}function ns(e,t,n,s){const r=n.delimitedEncoding,a=s??me(n.message,void 0,!1);return fi(a,e,t,r,r?n.number:e.uint32()),a}function lt(e,t){switch(t){case _.STRING:return e.string();case _.BOOL:return e.bool();case _.DOUBLE:return e.double();case _.FLOAT:return e.float();case _.INT32:return e.int32();case _.INT64:return e.int64();case _.UINT64:return e.uint64();case _.FIXED64:return e.fixed64();case _.BYTES:return e.bytes();case _.FIXED32:return e.fixed32();case _.SFIXED32:return e.sfixed32();case _.SFIXED64:return e.sfixed64();case _.SINT64:return e.sint64();case _.UINT32:return e.uint32();case _.SINT32:return e.sint32()}}function Lr(e,t){const n=Dr(yu,li(e));return n.messageType.forEach(Pr),n.dependency=[],ci(n,s=>{}).getFile(n.name)}const bu=zt(Lr("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),vu=3,Aa={writeUnknownFields:!0};function Su(e,t,n){return Nn(new Zo,function(s){return s?Object.assign(Object.assign({},Aa),s):Aa}(n),me(e,t)).finish()}function Nn(e,t,n){var s;for(const r of n.sortedFields)if(n.isSet(r))_i(e,t,n,r);else if(r.presence==vu)throw new Error(`cannot encode ${r} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:r,wireType:a,data:o}of(s=n.getUnknown())!==null&&s!==void 0?s:[])e.tag(r,a).raw(o);return e}function _i(e,t,n,s){var r;switch(s.fieldKind){case"scalar":case"enum":kn(e,n.desc.typeName,s.name,(r=s.scalar)!==null&&r!==void 0?r:_.INT32,s.number,n.get(s));break;case"list":(function(a,o,i,c){var l;if(i.listKind=="message"){for(const u of c)Ra(a,o,i,u);return}const d=(l=i.scalar)!==null&&l!==void 0?l:_.INT32;if(i.packed){if(!c.size)return;a.tag(i.number,$.LengthDelimited).fork();for(const u of c)bi(a,i.parent.typeName,i.name,d,u);return void a.join()}for(const u of c)kn(a,i.parent.typeName,i.name,d,i.number,u)})(e,t,s,n.get(s));break;case"message":Ra(e,t,s,n.get(s));break;case"map":for(const[a,o]of n.get(s))Eu(e,t,s,a,o)}}function kn(e,t,n,s,r,a){bi(e.tag(r,function(o){switch(o){case _.BYTES:case _.STRING:return $.LengthDelimited;case _.DOUBLE:case _.FIXED64:case _.SFIXED64:return $.Bit64;case _.FIXED32:case _.SFIXED32:case _.FLOAT:return $.Bit32;default:return $.Varint}}(s)),t,n,s,a)}function Ra(e,t,n,s){n.delimitedEncoding?Nn(e.tag(n.number,$.StartGroup),t,s).tag(n.number,$.EndGroup):Nn(e.tag(n.number,$.LengthDelimited).fork(),t,s).join()}function Eu(e,t,n,s,r){var a;switch(e.tag(n.number,$.LengthDelimited).fork(),kn(e,n.parent.typeName,n.name,n.mapKey,1,s),n.mapKind){case"scalar":case"enum":kn(e,n.parent.typeName,n.name,(a=n.scalar)!==null&&a!==void 0?a:_.INT32,2,r);break;case"message":Nn(e.tag(2,$.LengthDelimited).fork(),t,r).join()}e.join()}function bi(e,t,n,s,r){try{switch(s){case _.STRING:e.string(r);break;case _.BOOL:e.bool(r);break;case _.DOUBLE:e.double(r);break;case _.FLOAT:e.float(r);break;case _.INT32:e.int32(r);break;case _.INT64:e.int64(r);break;case _.UINT64:e.uint64(r);break;case _.FIXED64:e.fixed64(r);break;case _.BYTES:e.bytes(r);break;case _.FIXED32:e.fixed32(r);break;case _.SFIXED32:e.sfixed32(r);break;case _.SFIXED64:e.sfixed64(r);break;case _.SINT64:e.sint64(r);break;case _.UINT32:e.uint32(r);break;case _.SINT32:e.sint32(r)}}catch(a){throw a instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${a.message}`):a}}function Tu(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(Ma(e.typeUrl));return n&&function(s,r){return s.typeUrl!==""&&(typeof r=="string"?r:r.typeName)===Ma(s.typeUrl)}(e,n)?Dr(n,e.value):void 0}function Ma(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const Fr=Lr("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),wu=zt(Fr,0),vi=zt(Fr,1),Iu=zt(Fr,2);var $s;function Nu(e,t){Si(t,e);const n=function(o,i){if(o===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let c=o.length-1;c>=0;--c)if(o[c].no==i.number)return[o[c]];return[]}return o.filter(c=>c.no===i.number)}(e.$unknown,t),[s,r,a]=jn(t);for(const o of n)yi(s,new Cr(o.data),r,o.wireType,{readUnknownFields:!0});return a()}function ku(e,t,n){var s;Si(t,e);const r=((s=e.$unknown)!==null&&s!==void 0?s:[]).filter(l=>l.no!==t.number),[a,o]=jn(t,n),i=new Zo;_i(i,{writeUnknownFields:!0},a,o);const c=new Cr(i.finish());for(;c.pos<c.len;){const[l,d]=c.tag(),u=c.skip(d,l);r.push({no:l,wireType:d,data:u})}e.$unknown=r}function jn(e,t){const n=e.typeName,s=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),r=Object.assign(Object.assign({},e.extendee),{fields:[s],members:[s],oneofs:[]}),a=Ee(r,t!==void 0?{[n]:t}:void 0);return[me(r,a),s,()=>{const o=a[n];if(o===void 0){const i=e.message;return Wt(i)?nt(i.fields[0].scalar,i.fields[0].longAsString):Ee(i)}return o}]}function Si(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})($s||($s={}));const Cu=3,xu=2,Oa={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function Au(e,t,n){return Dt(me(e,t),function(s){return s?Object.assign(Object.assign({},Oa),s):Oa}(n))}function Dt(e,t){var n;const s=function(a,o){if(a.desc.typeName.startsWith("google.protobuf.")){switch(a.desc.typeName){case"google.protobuf.Any":return function(c,l){if(c.typeUrl==="")return{};const{registry:d}=l;let u,h;if(d&&(u=Tu(c,d),u&&(h=d.getMessage(u.$typeName))),!h||!u)throw new Error(`cannot encode message ${c.$typeName} to JSON: "${c.typeUrl}" is not in the type registry`);let p=Dt(me(h,u),l);return(h.typeName.startsWith("google.protobuf.")||p===null||Array.isArray(p)||typeof p!="object")&&(p={value:p}),p["@type"]=c.typeUrl,p}(a.message,o);case"google.protobuf.Timestamp":return function(c){const l=1e3*Number(c.seconds);if(l<Date.parse("0001-01-01T00:00:00Z")||l>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${c.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(c.nanos<0)throw new Error(`cannot encode message ${c.$typeName} to JSON: nanos must not be negative`);let d="Z";if(c.nanos>0){const u=(c.nanos+1e9).toString().substring(1);d=u.substring(3)==="000000"?"."+u.substring(0,3)+"Z":u.substring(6)==="000"?"."+u.substring(0,6)+"Z":"."+u+"Z"}return new Date(l).toISOString().replace(".000Z",d)}(a.message);case"google.protobuf.Duration":return function(c){if(Number(c.seconds)>315576e6||Number(c.seconds)<-315576e6)throw new Error(`cannot encode message ${c.$typeName} to JSON: value out of range`);let l=c.seconds.toString();if(c.nanos!==0){let d=Math.abs(c.nanos).toString();d="0".repeat(9-d.length)+d,d.substring(3)==="000000"?d=d.substring(0,3):d.substring(6)==="000"&&(d=d.substring(0,6)),l+="."+d,c.nanos<0&&Number(c.seconds)==0&&(l="-"+l)}return l+"s"}(a.message);case"google.protobuf.FieldMask":return(i=a.message).paths.map(c=>{if(c.match(/_[0-9]?_/g)||c.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+c+'" is irreversible');return Bt(c)}).join(",");case"google.protobuf.Struct":return Ei(a.message);case"google.protobuf.Value":return Ur(a.message);case"google.protobuf.ListValue":return Ti(a.message);default:if(Wt(a.desc)){const c=a.desc.fields[0];return fn(c,a.get(c))}return}var i}}(e,t);if(s!==void 0)return s;const r={};for(const a of e.sortedFields){if(!e.isSet(a)){if(a.presence==Cu)throw new Error(`cannot encode ${a} to JSON: required field not set`);if(!t.alwaysEmitImplicit||a.presence!==xu)continue}const o=Pa(a,e.get(a),t);o!==void 0&&(r[Ru(a,t)]=o)}if(t.registry){const a=new Set;for(const{no:o}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!a.has(o)){a.add(o);const i=t.registry.getExtensionFor(e.desc,o);if(!i)continue;const c=Nu(e.message,i),[l,d]=jn(i,c),u=Pa(d,l.get(d),t);u!==void 0&&(r[i.jsonName]=u)}}return r}function Pa(e,t,n){switch(e.fieldKind){case"scalar":return fn(e,t);case"message":return Dt(t,n);case"enum":return ss(e.enum,t,n.enumAsInteger);case"list":return function(s,r){const a=s.field(),o=[];switch(a.listKind){case"scalar":for(const i of s)o.push(fn(a,i));break;case"enum":for(const i of s)o.push(ss(a.enum,i,r.enumAsInteger));break;case"message":for(const i of s)o.push(Dt(i,r))}return r.alwaysEmitImplicit||o.length>0?o:void 0}(t,n);case"map":return function(s,r){const a=s.field(),o={};switch(a.mapKind){case"scalar":for(const[i,c]of s)o[i]=fn(a,c);break;case"message":for(const[i,c]of s)o[i]=Dt(c,r);break;case"enum":for(const[i,c]of s)o[i]=ss(a.enum,c,r.enumAsInteger)}return r.alwaysEmitImplicit||s.size>0?o:void 0}(t,n)}}function ss(e,t,n){var s;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${G(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const r=e.value[t];return(s=r==null?void 0:r.name)!==null&&s!==void 0?s:t}function fn(e,t){var n,s,r,a,o,i;switch(e.scalar){case _.INT32:case _.SFIXED32:case _.SINT32:case _.FIXED32:case _.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=Je(e,t))===null||n===void 0?void 0:n.message}`);return t;case _.FLOAT:case _.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(s=Je(e,t))===null||s===void 0?void 0:s.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case _.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(r=Je(e,t))===null||r===void 0?void 0:r.message}`);return t;case _.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(a=Je(e,t))===null||a===void 0?void 0:a.message}`);return t;case _.UINT64:case _.FIXED64:case _.INT64:case _.SFIXED64:case _.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(o=Je(e,t))===null||o===void 0?void 0:o.message}`);return t.toString();case _.BYTES:if(t instanceof Uint8Array)return function(c,l="std"){const d=ui(l),u=l=="std";let h,p="",m=0,y=0;for(let f=0;f<c.length;f++)switch(h=c[f],m){case 0:p+=d[h>>2],y=(3&h)<<4,m=1;break;case 1:p+=d[y|h>>4],y=(15&h)<<2,m=2;break;case 2:p+=d[y|h>>6],p+=d[63&h],m=0}return m&&(p+=d[y],u&&(p+="=",m==1&&(p+="="))),p}(t);throw new Error(`cannot encode ${e} to JSON: ${(i=Je(e,t))===null||i===void 0?void 0:i.message}`)}}function Ru(e,t){return t.useProtoFieldName?e.name:e.jsonName}function Ei(e){const t={};for(const[n,s]of Object.entries(e.fields))t[n]=Ur(s);return t}function Ur(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return Ei(e.kind.value);case"listValue":return Ti(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function Ti(e){return e.values.map(Ur)}const Da={ignoreUnknownFields:!1};function Mu(e,t,n){const s=me(e);try{_t(s,t,function(a){return a?Object.assign(Object.assign({},Da),a):Da}(n))}catch(a){throw(r=a)instanceof Error&&jl.includes(r.name)&&"field"in r&&typeof r.field=="function"?new Error(`cannot decode ${a.field()} from JSON: ${a.message}`,{cause:a}):a}var r;return s.message}function _t(e,t,n){var s;if(function(o,i,c){if(!o.desc.typeName.startsWith("google.protobuf."))return!1;switch(o.desc.typeName){case"google.protobuf.Any":return function(l,d,u){var h;if(d===null||Array.isArray(d)||typeof d!="object")throw new Error(`cannot decode message ${l.$typeName} from JSON: expected object but got ${G(d)}`);if(Object.keys(d).length==0)return;const p=d["@type"];if(typeof p!="string"||p=="")throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is empty`);const m=p.includes("/")?p.substring(p.lastIndexOf("/")+1):p;if(!m.length)throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is invalid`);const y=(h=u.registry)===null||h===void 0?void 0:h.getMessage(m);if(!y)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${p} is not in the type registry`);const f=me(y);if(m.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(d,"value"))_t(f,d.value,u);else{const b=Object.assign({},d);delete b["@type"],_t(f,b,u)}(function(b,S,v){let T=!1;v||(v=Ee(bu),T=!0),v.value=Su(b,S),v.typeUrl=`type.googleapis.com/${S.$typeName}`})(f.desc,f.message,l)}(o.message,i,c),!0;case"google.protobuf.Timestamp":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);const u=d.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!u)throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);const h=Date.parse(u[1]+"-"+u[2]+"-"+u[3]+"T"+u[4]+":"+u[5]+":"+u[6]+(u[8]?u[8]:"Z"));if(Number.isNaN(h))throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);if(h<Date.parse("0001-01-01T00:00:00Z")||h>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${l.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);l.seconds=L.parse(h/1e3),l.nanos=0,u[7]&&(l.nanos=parseInt("1"+u[7]+"0".repeat(9-u[7].length))-1e9)}(o.message,i),!0;case"google.protobuf.Duration":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);const u=d.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(u===null)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);const h=Number(u[1]);if(h>315576e6||h<-315576e6)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);if(l.seconds=L.parse(h),typeof u[2]!="string")return;const p=u[2]+"0".repeat(9-u[2].length);l.nanos=parseInt(p),(h<0||Object.is(h,-0))&&(l.nanos=-l.nanos)}(o.message,i),!0;case"google.protobuf.FieldMask":return function(l,d){if(typeof d!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${G(d)}`);if(d==="")return;function u(h){if(h.includes("_"))throw new Error(`cannot decode message ${l.$typeName} from JSON: path names must be lowerCamelCase`);const p=h.replace(/[A-Z]/g,m=>"_"+m.toLowerCase());return p[0]==="_"?p.substring(1):p}l.paths=d.split(",").map(u)}(o.message,i),!0;case"google.protobuf.Struct":return Ii(o.message,i),!0;case"google.protobuf.Value":return $r(o.message,i),!0;case"google.protobuf.ListValue":return Ni(o.message,i),!0;default:if(Wt(o.desc)){const l=o.desc.fields[0];return i===null?o.clear(l):o.set(l,_n(l,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${G(t)}`);const r=new Map,a=new Map;for(const o of e.desc.fields)a.set(o.name,o).set(o.jsonName,o);for(const[o,i]of Object.entries(t)){const c=a.get(o);if(c){if(c.oneof){if(i===null&&c.fieldKind=="scalar")continue;const l=r.get(c.oneof);if(l!==void 0)throw new ie(c.oneof,`oneof set multiple times by ${l.name} and ${c.name}`);r.set(c.oneof,c)}La(e,c,i,n)}else{let l;if(o.startsWith("[")&&o.endsWith("]")&&(l=(s=n.registry)===null||s===void 0?void 0:s.getExtension(o.substring(1,o.length-1)))&&l.extendee.typeName===e.desc.typeName){const[d,u,h]=jn(l);La(d,u,i,n),ku(e.message,l,h())}if(!l&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${o}" is unknown`)}}}function La(e,t,n,s){switch(t.fieldKind){case"scalar":(function(r,a,o){const i=_n(a,o,!1);i===Cn?r.clear(a):r.set(a,i)})(e,t,n);break;case"enum":(function(r,a,o,i){const c=rs(a.enum,o,i.ignoreUnknownFields,!1);c===Cn?r.clear(a):c!==yn&&r.set(a,c)})(e,t,n,s);break;case"message":(function(r,a,o,i){if(o===null&&a.message.typeName!="google.protobuf.Value")return void r.clear(a);const c=r.isSet(a)?r.get(a):me(a.message);_t(c,o,i),r.set(a,c)})(e,t,n,s);break;case"list":(function(r,a,o){if(a===null)return;const i=r.field();if(!Array.isArray(a))throw new ie(i,"expected Array, got "+G(a));for(const c of a){if(c===null)throw new ie(i,"list item must not be null");switch(i.listKind){case"message":const l=me(i.message);_t(l,c,o),r.add(l);break;case"enum":const d=rs(i.enum,c,o.ignoreUnknownFields,!0);d!==yn&&r.add(d);break;case"scalar":r.add(_n(i,c,!0))}}})(e.get(t),n,s);break;case"map":(function(r,a,o){if(a===null)return;const i=r.field();if(typeof a!="object"||Array.isArray(a))throw new ie(i,"expected object, got "+G(a));for(const[c,l]of Object.entries(a)){if(l===null)throw new ie(i,"map value must not be null");let d;switch(i.mapKind){case"message":const h=me(i.message);_t(h,l,o),d=h;break;case"enum":if(d=rs(i.enum,l,o.ignoreUnknownFields,!0),d===yn)return;break;case"scalar":d=_n(i,l,!0)}const u=Ou(i.mapKey,c);r.set(u,d)}})(e.get(t),n,s)}}const yn=Symbol();function rs(e,t,n,s){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:s?e.values[0].number:Cn;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const r=e.values.find(a=>a.name===t);if(r!==void 0)return r.number;if(n)return yn}throw new Error(`cannot decode ${e} from JSON: ${G(t)}`)}const Cn=Symbol();function _n(e,t,n){if(t===null)return n?nt(e.scalar,!1):Cn;switch(e.scalar){case _.DOUBLE:case _.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new ie(e,"unexpected NaN number");if(!Number.isFinite(t))throw new ie(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const s=Number(t);if(!Number.isFinite(s))break;return s}break;case _.INT32:case _.FIXED32:case _.SFIXED32:case _.SINT32:case _.UINT32:return wi(t);case _.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return li(t)}catch(s){const r=s instanceof Error?s.message:String(s);throw new ie(e,r)}}}return t}function Ou(e,t){switch(e){case _.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case _.INT32:case _.FIXED32:case _.UINT32:case _.SFIXED32:case _.SINT32:return wi(t);default:return t}}function wi(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function Ii(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${G(t)}`);for(const[n,s]of Object.entries(t)){const r=Ee(vi);$r(r,s),e.fields[n]=r}}function $r(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:$s.NULL_VALUE};else if(Array.isArray(t)){const n=Ee(Iu);Ni(n,t),e.kind={case:"listValue",value:n}}else{const n=Ee(wu);Ii(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${G(t)}`)}return e}function Ni(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${G(t)}`);for(const n of t){const s=Ee(vi);$r(s,n),e.values.push(s)}}class ki{constructor(t){g(this,"pendingRequests",new Map);g(this,"cleanup");g(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,s=>{this.target.sendMessage(s)})}catch(s){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:s instanceof Error?s.message:String(s)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(s){const r=s instanceof Error?s.message:String(s);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${r}`))}}sendRequest(t,n){return new Promise((s,r)=>{let a;n&&(a=setTimeout(()=>{this.pendingRequests.delete(t.id),r(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:s,reject:r,timeout:a}),this.target.sendMessage(t)})}async unary(t,n,s,r,a,o){const i=crypto.randomUUID(),c=t.localName,l=t.parent.typeName;if(!l)throw new Error("Service name is required for unary calls");const d=a?Au(t.input,Ee(t.input,a)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${l}.${c} (ID: ${i})`);let u;n&&(u=()=>{const p=this.pendingRequests.get(i);p&&(this.pendingRequests.delete(i),clearTimeout(p.timeout),p.reject(new Error(`gRPC request aborted during execution: ${l}.${c} (ID: ${i})`)))},n.addEventListener("abort",u));const h=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:c,serviceTypeName:l,data:d,timeout:s},s);return n&&u&&n.removeEventListener("abort",u),{stream:!1,method:t,service:t.parent,header:new Headers(r),message:Mu(t.output,h.data),trailer:new Headers}}stream(t,n,s,r,a,o){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}g(ki,"PROTOCOL_NAME","com.augmentcode.client.rpc");var We;function Fa(e){const t=We[e];return typeof t!="string"?e.toString():t[0].toLowerCase()+t.substring(1).replace(/[A-Z]/g,n=>"_"+n.toLowerCase())}(function(e){e[e.Canceled=1]="Canceled",e[e.Unknown=2]="Unknown",e[e.InvalidArgument=3]="InvalidArgument",e[e.DeadlineExceeded=4]="DeadlineExceeded",e[e.NotFound=5]="NotFound",e[e.AlreadyExists=6]="AlreadyExists",e[e.PermissionDenied=7]="PermissionDenied",e[e.ResourceExhausted=8]="ResourceExhausted",e[e.FailedPrecondition=9]="FailedPrecondition",e[e.Aborted=10]="Aborted",e[e.OutOfRange=11]="OutOfRange",e[e.Unimplemented=12]="Unimplemented",e[e.Internal=13]="Internal",e[e.Unavailable=14]="Unavailable",e[e.DataLoss=15]="DataLoss",e[e.Unauthenticated=16]="Unauthenticated"})(We||(We={}));class Ve extends Error{constructor(t,n=We.Unknown,s,r,a){super(function(o,i){return o.length?`[${Fa(i)}] ${o}`:`[${Fa(i)}]`}(t,n)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=t,this.code=n,this.metadata=new Headers(s??{}),this.details=r??[],this.cause=a}static from(t,n=We.Unknown){return t instanceof Ve?t:t instanceof Error?t.name=="AbortError"?new Ve(t.message,We.Canceled):new Ve(t.message,n,void 0,void 0,t):new Ve(String(t),n,void 0,void 0,t)}static[Symbol.hasInstance](t){return t instanceof Error&&(Object.getPrototypeOf(t)===Ve.prototype||t.name==="ConnectError"&&"code"in t&&typeof t.code=="number"&&"metadata"in t&&"details"in t&&Array.isArray(t.details)&&"rawMessage"in t&&typeof t.rawMessage=="string"&&"cause"in t)}findDetails(t){const n=t.kind==="message"?{getMessage:r=>r===t.typeName?t:void 0}:t,s=[];for(const r of this.details){if("desc"in r){n.getMessage(r.desc.typeName)&&s.push(Ee(r.desc,r.value));continue}const a=n.getMessage(r.type);if(a)try{s.push(Dr(a,r.value))}catch{}}return s}}var Pu=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(r){t[r]=e[r]&&function(a){return new Promise(function(o,i){(function(c,l,d,u){Promise.resolve(u).then(function(h){c({value:h,done:d})},l)})(o,i,(a=e[r](a)).done,a.value)})}}},Vt=function(e){return this instanceof Vt?(this.v=e,this):new Vt(e)},Du=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,r=n.apply(e,t||[]),a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(u){return function(h){return Promise.resolve(h).then(u,l)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(u,h){r[u]&&(s[u]=function(p){return new Promise(function(m,y){a.push([u,p,m,y])>1||i(u,p)})},h&&(s[u]=h(s[u])))}function i(u,h){try{(p=r[u](h)).value instanceof Vt?Promise.resolve(p.value.v).then(c,l):d(a[0][2],p)}catch(m){d(a[0][3],m)}var p}function c(u){i("next",u)}function l(u){i("throw",u)}function d(u,h){u(h),a.shift(),a.length&&i(a[0][0],a[0][1])}},Lu=function(e){var t,n;return t={},s("next"),s("throw",function(r){throw r}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(r,a){t[r]=e[r]?function(o){return(n=!n)?{value:Vt(e[r](o)),done:!1}:a?a(o):o}:a}},Ci=function(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),t={},s("next"),s("throw"),s("return"),t[Symbol.asyncIterator]=function(){return this},t);function s(r){t[r]=e[r]&&function(a){return new Promise(function(o,i){(function(c,l,d,u){Promise.resolve(u).then(function(h){c({value:h,done:d})},l)})(o,i,(a=e[r](a)).done,a.value)})}}},vt=function(e){return this instanceof vt?(this.v=e,this):new vt(e)},Fu=function(e){var t,n;return t={},s("next"),s("throw",function(r){throw r}),s("return"),t[Symbol.iterator]=function(){return this},t;function s(r,a){t[r]=e[r]?function(o){return(n=!n)?{value:vt(e[r](o)),done:!1}:a?a(o):o}:a}},Uu=function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,r=n.apply(e,t||[]),a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),o("next"),o("throw"),o("return",function(u){return function(h){return Promise.resolve(h).then(u,l)}}),s[Symbol.asyncIterator]=function(){return this},s;function o(u,h){r[u]&&(s[u]=function(p){return new Promise(function(m,y){a.push([u,p,m,y])>1||i(u,p)})},h&&(s[u]=h(s[u])))}function i(u,h){try{(p=r[u](h)).value instanceof vt?Promise.resolve(p.value.v).then(c,l):d(a[0][2],p)}catch(m){d(a[0][3],m)}var p}function c(u){i("next",u)}function l(u){i("throw",u)}function d(u,h){u(h),a.shift(),a.length&&i(a[0][0],a[0][1])}};function $u(e,t){return function(n,s){const r={};for(const a of n.methods){const o=s(a);o!=null&&(r[a.localName]=o)}return r}(e,n=>{switch(n.methodKind){case"unary":return function(s,r){return async function(a,o){var i,c;const l=await s.unary(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues);return(i=o==null?void 0:o.onHeader)===null||i===void 0||i.call(o,l.header),(c=o==null?void 0:o.onTrailer)===null||c===void 0||c.call(o,l.trailer),l.message}}(t,n);case"server_streaming":return function(s,r){return function(a,o){return Ua(s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,function(i){return Du(this,arguments,function*(){yield Vt(yield*Lu(Pu(i)))})}([a]),o==null?void 0:o.contextValues),o)}}(t,n);case"client_streaming":return function(s,r){return async function(a,o){var i,c,l,d,u,h;const p=await s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues);let m;(u=o==null?void 0:o.onHeader)===null||u===void 0||u.call(o,p.header);let y=0;try{for(var f,b=!0,S=Ci(p.message);!(i=(f=await S.next()).done);b=!0)d=f.value,b=!1,m=d,y++}catch(v){c={error:v}}finally{try{b||i||!(l=S.return)||await l.call(S)}finally{if(c)throw c.error}}if(!m)throw new Ve("protocol error: missing response message",We.Unimplemented);if(y>1)throw new Ve("protocol error: received extra messages for client streaming method",We.Unimplemented);return(h=o==null?void 0:o.onTrailer)===null||h===void 0||h.call(o,p.trailer),m}}(t,n);case"bidi_streaming":return function(s,r){return function(a,o){return Ua(s.stream(r,o==null?void 0:o.signal,o==null?void 0:o.timeoutMs,o==null?void 0:o.headers,a,o==null?void 0:o.contextValues),o)}}(t,n);default:return null}})}function Ua(e,t){const n=function(){return Uu(this,arguments,function*(){var s,r;const a=yield vt(e);(s=t==null?void 0:t.onHeader)===null||s===void 0||s.call(t,a.header),yield vt(yield*Fu(Ci(a.message))),(r=t==null?void 0:t.onTrailer)===null||r===void 0||r.call(t,a.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>n.next()})}}function qu(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}async function $a(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}var xi=(e=>(e.chat="chat",e))(xi||{}),Ai=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(Ai||{});function qa(e){return e.replace(/^data:.*?;base64,/,"")}async function as(e){return new Promise((t,n)=>{const s=new FileReader;s.onload=r=>{var a;return t((a=r.target)==null?void 0:a.result)},s.onerror=n,s.readAsDataURL(e)})}async function os(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,s=>s.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const s=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));s.onmessage=function(r){r.data.error?n(new Error(r.data.error)):t(r.data),s.terminate()},s.onerror=function(r){n(r.error),s.terminate()},s.postMessage(e)})}const Hu=qu(Lr("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var j=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e.getSoundSettings="get-sound-settings",e.getSoundSettingsResponse="get-sound-settings-response",e.updateSoundSettings="update-sound-settings",e.soundSettingsBroadcast="sound-settings-broadcast",e.getSwarmModeSettings="get-swarm-mode-settings",e.getSwarmModeSettingsResponse="get-swarm-mode-settings-response",e.updateSwarmModeSettings="update-swarm-mode-settings",e.swarmModeSettingsBroadcast="swarm-mode-settings-broadcast",e.getChatModeRequest="get-chat-mode-request",e.getChatModeResponse="get-chat-mode-response",e))(j||{}),bn=(e=>(e.checkToolCallSafeRequest="check-tool-call-safe-request",e.checkToolCallSafeResponse="check-tool-call-safe-response",e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(bn||{}),vn=(e=>(e.loadConversationExchangesRequest="load-conversation-exchanges-request",e.loadConversationExchangesResponse="load-conversation-exchanges-response",e.loadExchangesByUuidsRequest="load-exchanges-by-uuids-request",e.loadExchangesByUuidsResponse="load-exchanges-by-uuids-response",e.saveExchangesRequest="save-exchanges-request",e.saveExchangesResponse="save-exchanges-response",e.deleteExchangesRequest="delete-exchanges-request",e.deleteExchangesResponse="delete-exchanges-response",e.deleteConversationExchangesRequest="delete-conversation-exchanges-request",e.deleteConversationExchangesResponse="delete-conversation-exchanges-response",e.countExchangesRequest="count-exchanges-request",e.countExchangesResponse="count-exchanges-response",e))(vn||{});class Bu{constructor(t=[]){g(this,"_items",[]);g(this,"_focusedItemIdx");g(this,"_subscribers",new Set);g(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));g(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});g(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const n=t?this._items.indexOf(t):-1;n===-1?this.setFocusIdx(void 0):this.setFocusIdx(n)});g(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const n=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-n)%this._items.length,this.notifySubscribers()});g(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));g(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});g(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});g(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});g(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});g(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}var Gu=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Gu||{}),Vu=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(Vu||{});function ju(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Ri{constructor(){g(this,"tracingData",{flags:{},nums:{},string_stats:{},request_ids:{}})}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:ju(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var Yu=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(Yu||{}),Ku=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(Ku||{});class Mi extends Ri{constructor(){super()}static create(){return new Mi}}var Wu=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalCwdNotAbsolute="vs-code-terminal-cwd-not-absolute",e.vsCodeTerminalCwdDoesNotExist="vs-code-terminal-cwd-does-not-exist",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalTimedOutWaitingForSetCwdCommand="vs-code-terminal-timed-out-waiting-for-set-cwd-command",e.vsCodeTerminalTimedOutWaitingForStartupCommand="vs-code-terminal-timed-out-waiting-for-startup-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeScriptStrategyPollingDeterminedProcessIsDone="vs-code-script-strategy-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.vsCodeTerminalScriptCommandNotAvailable="vs-code-terminal-script-command-not-available",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e.taskListUsage="task-list-usage",e.memoryUsage="memory-usage",e.contentTruncation="content-truncation",e.modelSelectionChange="model-selection-change",e))(Wu||{}),xn=(e=>(e.sentUserMessage="sent-user-message",e.chatHistorySummarization="chat-history-summarization",e.enhancedPrompt="enhanced-prompt",e.firstTokenReceived="first-token-received",e.chatHistoryTruncated="chat-history-truncated",e))(xn||{}),zu=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(zu||{});class Oi extends Ri{constructor(){super()}static create(){return new Oi}}var Xu=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e.modeSelector="mode-selector",e.remoteAgentSetupWindow="remote-agent-setup-window",e.remoteAgentThreadList="remote-agent-thread-list",e.remoteAgentNewThreadButton="remote-agent-new-thread-button",e))(Xu||{}),Ju=(e=>(e[e.unknownSourceControl=0]="unknownSourceControl",e[e.git=1]="git",e[e.github=2]="github",e))(Ju||{}),Zu=(e=>(e[e.unknownMode=0]="unknownMode",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(Zu||{}),Qu=(e=>(e[e.unknownModeSelectorAction=0]="unknownModeSelectorAction",e[e.open=1]="open",e[e.close=2]="close",e[e.select=3]="select",e[e.init=4]="init",e))(Qu||{}),ec=(e=>(e[e.unknownSetupWindowAction=0]="unknownSetupWindowAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectRepo=3]="selectRepo",e[e.selectBranch=4]="selectBranch",e[e.selectSetupScript=5]="selectSetupScript",e[e.autoGenerateSetupScript=6]="autoGenerateSetupScript",e[e.manuallyCreateSetupScript=7]="manuallyCreateSetupScript",e[e.typeInPromptWindow=8]="typeInPromptWindow",e[e.clickRewritePrompt=9]="clickRewritePrompt",e[e.enableNotifications=10]="enableNotifications",e[e.disableNotifications=11]="disableNotifications",e[e.clickCreateAgent=12]="clickCreateAgent",e))(ec||{}),tc=(e=>(e[e.unknownAgentListAction=0]="unknownAgentListAction",e[e.open=1]="open",e[e.close=2]="close",e[e.selectAgent=3]="selectAgent",e[e.deleteAgent=4]="deleteAgent",e[e.pinAgent=5]="pinAgent",e[e.unpinAgent=6]="unpinAgent",e))(tc||{}),nc=(e=>(e[e.unknown=0]="unknown",e[e.click=1]="click",e[e.open=2]="open",e[e.close=3]="close",e))(nc||{}),sc=(e=>(e[e.unknown=0]="unknown",e[e.chat=1]="chat",e[e.agent=2]="agent",e[e.remoteAgent=3]="remoteAgent",e))(sc||{}),rc=(e=>(e[e.unknown=0]="unknown",e[e.addTask=1]="addTask",e[e.addSubtask=2]="addSubtask",e[e.updateTaskStatus=3]="updateTaskStatus",e[e.updateTaskName=4]="updateTaskName",e[e.updateTaskDescription=5]="updateTaskDescription",e[e.reorganizeTaskList=6]="reorganizeTaskList",e[e.deleteTask=7]="deleteTask",e[e.runSingleTask=8]="runSingleTask",e[e.runAllTasks=9]="runAllTasks",e[e.viewTaskList=10]="viewTaskList",e[e.exportTaskList=11]="exportTaskList",e[e.importTaskList=12]="importTaskList",e[e.syncTaskList=13]="syncTaskList",e))(rc||{}),ac=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(ac||{}),oc=(e=>(e[e.unknown=0]="unknown",e[e.saveMemory=1]="saveMemory",e[e.discardMemory=2]="discardMemory",e[e.editMemory=3]="editMemory",e[e.viewMemories=4]="viewMemories",e[e.refreshMemories=5]="refreshMemories",e[e.filterByState=6]="filterByState",e[e.filterByVersion=7]="filterByVersion",e[e.openMemoriesFile=8]="openMemoriesFile",e[e.createMemory=9]="createMemory",e))(oc||{}),ic=(e=>(e[e.unknown=0]="unknown",e[e.user=1]="user",e[e.agent=2]="agent",e))(ic||{});function lc(e,t,n=1e3){let s=null,r=0;const a=Ht(t),o=()=>{const i=(()=>{const c=Date.now();if(s!==null&&c-r<n)return s;const l=e();return s=l,r=c,l})();a.set(i)};return{subscribe:a.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Pi=(e=>(e[e.unset=0]="unset",e[e.positive=1]="positive",e[e.negative=2]="negative",e))(Pi||{}),uc=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(uc||{}),cc=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(cc||{});class he{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,s=!0){const r=this.extractFrontmatter(t);if(r){const a=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),o=r.match(a);if(o&&o[1])return o[1].toLowerCase()==="true"}return s}static parseString(t,n,s=""){const r=this.extractFrontmatter(t);if(r){const a=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),o=r.match(a);if(o&&o[1])return o[1].trim()}return s}static updateFrontmatter(t,n,s){const r=t.match(this.frontmatterRegex),a=typeof s!="string"||/^(true|false)$/.test(s.toLowerCase())?String(s):`"${s}"`;if(r){const o=r[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(o.match(i)){const c=o.replace(i,`$1${a}`);return t.replace(this.frontmatterRegex,`---
${c}---
`)}{const c=`${o.endsWith(`
`)?o:o+`
`}${n}: ${a}
`;return t.replace(this.frontmatterRegex,`---
${c}---
`)}}return`---
${n}: ${a}
---

${t}`}static createFrontmatter(t,n){let s=t;this.hasFrontmatter(s)&&(s=this.extractContent(s));for(const[r,a]of Object.entries(n))s=this.updateFrontmatter(s,r,a);return s}}g(he,"frontmatterRegex",/^---\s*\n([\s\S]*?)\n---\s*\n/);class xe{static parseRuleFile(t,n){const s=he.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,""),r=he.extractContent(t);return{type:this.getRuleTypeFromContent(t),path:n,content:r,description:s||void 0}}static formatRuleFileForMarkdown(t){let n=t.content;return n=he.updateFrontmatter(n,this.TYPE_FRONTMATTER_KEY,this.mapRuleTypeToString(t.type)),t.description&&(n=he.updateFrontmatter(n,this.DESCRIPTION_FRONTMATTER_KEY,t.description)),n}static getAlwaysApplyFrontmatterKey(t){return he.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return he.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return he.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}static getDescriptionFrontmatterKey(t){return he.parseString(t,this.DESCRIPTION_FRONTMATTER_KEY,"")}static updateDescriptionFrontmatterKey(t,n){return he.updateFrontmatter(t,this.DESCRIPTION_FRONTMATTER_KEY,n)}static mapStringToRuleType(t){switch(t.toLowerCase()){case"always_apply":return ne.ALWAYS_ATTACHED;case"manual":return ne.MANUAL;case"agent_requested":return ne.AGENT_REQUESTED;default:return this.DEFAULT_RULE_TYPE}}static mapRuleTypeToString(t){switch(t){case ne.ALWAYS_ATTACHED:return"always_apply";case ne.MANUAL:return"manual";case ne.AGENT_REQUESTED:return"agent_requested";default:return"manual"}}static isValidTypeValue(t){return this.VALID_TYPE_VALUES.includes(t.toLowerCase())}static getTypeFrontmatterKey(t){return he.parseString(t,this.TYPE_FRONTMATTER_KEY,"")}static updateTypeFrontmatterKey(t,n){const s=this.mapRuleTypeToString(n);return he.updateFrontmatter(t,this.TYPE_FRONTMATTER_KEY,s)}static getRuleTypeFromContent(t){const n=this.getTypeFrontmatterKey(t);if(n&&this.isValidTypeValue(n))return this.mapStringToRuleType(n);const s=this.getAlwaysApplyFrontmatterKey(t),r=this.getDescriptionFrontmatterKey(t);return s?ne.ALWAYS_ATTACHED:r&&r.trim()!==""?ne.AGENT_REQUESTED:ne.MANUAL}}g(xe,"ALWAYS_APPLY_FRONTMATTER_KEY","alwaysApply"),g(xe,"DESCRIPTION_FRONTMATTER_KEY","description"),g(xe,"TYPE_FRONTMATTER_KEY","type"),g(xe,"VALID_TYPE_VALUES",["always_apply","manual","agent_requested"]),g(xe,"DEFAULT_RULE_TYPE",ne.MANUAL);const ut=".augment",xt="rules",is=".augment-guidelines";function ae(e,t){return t in e&&e[t]!==void 0}function dc(e){return ae(e,"file")}function hc(e){return ae(e,"recentFile")}function pc(e){return ae(e,"folder")}function mc(e){return ae(e,"sourceFolder")}function Ip(e){return ae(e,"sourceFolderGroup")}function Np(e){return ae(e,"selection")}function gc(e){return ae(e,"externalSource")}function kp(e){return ae(e,"allDefaultContext")}function Cp(e){return ae(e,"clearContext")}function xp(e){return ae(e,"userGuidelines")}function Ap(e){return ae(e,"agentMemories")}function Di(e){return ae(e,"personality")}function fc(e){return ae(e,"rule")}function yc(e){return ae(e,"task")}const Rp={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Mp={clearContext:!0,label:"Clear Context",id:"clearContext"},Op={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Pp={agentMemories:{},label:"Agent Memories",id:"agentMemories"},Ha=[{personality:{type:z.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:z.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:z.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:z.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Dp(e){return ae(e,"group")}function Lp(e){const t=new Map;return e.forEach(n=>{dc(n)?t.set("file",[...t.get("file")??[],n]):hc(n)?t.set("recentFile",[...t.get("recentFile")??[],n]):pc(n)?t.set("folder",[...t.get("folder")??[],n]):gc(n)?t.set("externalSource",[...t.get("externalSource")??[],n]):mc(n)?t.set("sourceFolder",[...t.get("sourceFolder")??[],n]):Di(n)?t.set("personality",[...t.get("personality")??[],n]):fc(n)&&t.set("rule",[...t.get("rule")??[],n])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:t.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:t.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:t.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:t.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:t.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:t.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:t.get("rule")??[]}}].filter(n=>n.group.items.length>0)}function _c(e){const t=(n={rootPath:e.repoRoot,relPath:e.pathName}).rootPath+"/"+n.relPath;var n;const s={label:_l(e.pathName).split("/").filter(r=>r.trim()!=="").pop()||"",name:t,id:t};if(e.fullRange){const r=`:L${e.fullRange.startLineNumber}-${e.fullRange.endLineNumber}`;s.label+=r,s.name+=r,s.id+=r}else if(e.range){const r=`:L${e.range.start}-${e.range.stop}`;s.label+=r,s.name+=r,s.id+=r}return s}function bc(e){const t=e.path.split("/"),n=t[t.length-1],s=n.endsWith(".md")?n.slice(0,-3):n,r=`${ut}/${xt}/${e.path}`;return{label:s,name:r,id:r}}var M=(e=>(e[e.unknown=0]="unknown",e[e.new=1]="new",e[e.checkingSafety=2]="checkingSafety",e[e.runnable=3]="runnable",e[e.running=4]="running",e[e.completed=5]="completed",e[e.error=6]="error",e[e.cancelling=7]="cancelling",e[e.cancelled=8]="cancelled",e))(M||{});function ls(e){return`${e.requestId};${e.toolUseId}`}function Ba(e){const[t,n]=e.split(";");return{requestId:t,toolUseId:n}}var vc=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e.gitCommitRetrieval="git-commit-retrieval",e.memoryRetrieval="memory-retrieval",e.startWorkerAgent="start_worker_agent",e.readWorkerState="read_worker_state",e.waitForWorkerAgent="wait_for_worker_agent",e.sendInstructionToWorkerAgent="send_instruction_to_worker_agent",e.stopWorkerAgent="stop_worker_agent",e.deleteWorkerAgent="delete_worker_agent",e.readWorkerAgentEdits="read_worker_agent_edits",e.applyWorkerAgentEdits="apply_worker_agent_edits",e.LocalSubAgent="local-sub-agent",e))(vc||{}),Sc=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(Sc||{}),An=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(An||{}),Ec=(e=>(e[e.Unsafe=0]="Unsafe",e[e.Safe=1]="Safe",e[e.Check=2]="Check",e))(Ec||{}),Tc=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(Tc||{});function Ga(e,t){return function(n,s){if(n.length<=s||n.length===0)return{truncatedText:n};const r=n.split(`
`),a="... additional lines truncated ..."+(r[0].endsWith("\r")?"\r":"");let o,i="";if(r.length<2||r[0].length+r[r.length-1].length+a.length>s){const c=Math.floor(s/2);i=[n.slice(0,c),"<...>",n.slice(-c)].join(""),o=[1,1,r.length,r.length]}else{const c=[],l=[];let d=a.length+1;for(let u=0;u<Math.floor(r.length/2);u++){const h=r[u],p=r[r.length-1-u],m=h.length+p.length+2;if(d+m>s)break;d+=m,c.push(h),l.push(p)}o=[1,c.length,r.length-l.length+1,r.length],c.push(a),c.push(...l.reverse()),i=c.join(`
`)}return{truncatedText:i,shownRangeWhenTruncated:o}}(e,t).truncatedText}function wc(e){var n;if(!e)return Zt.IMAGE_FORMAT_UNSPECIFIED;switch((n=e.split("/")[1])==null?void 0:n.toLowerCase()){case"jpeg":case"jpg":return Zt.JPEG;case"png":return Zt.PNG;default:return Zt.IMAGE_FORMAT_UNSPECIFIED}}function Ic(e,t,n){var r,a;if(e.phase!==M.cancelled&&e.phase!==M.completed&&e.phase!==M.error)return;let s;return(r=e.result)!=null&&r.contentNodes?(s=function(o,i){return o.map(c=>c.type===An.ContentText?{type:zn.CONTENT_TEXT,text_content:c.text_content}:c.type===An.ContentImage&&c.image_content&&i?{type:zn.CONTENT_IMAGE,image_content:{image_data:c.image_content.image_data,format:wc(c.image_content.media_type)}}:{type:zn.CONTENT_TEXT,text_content:"[Error: Invalid content node]"})}(e.result.contentNodes,n),{content:"",is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t,content_nodes:s}):((a=e.result)==null?void 0:a.text)!==void 0?{content:e.result.text,is_error:e.result.isError,request_id:e.result.requestId,tool_use_id:t}:void 0}function Nc(e=[]){let t;for(const n of e){if(n.type===R.TOOL_USE)return n;n.type===R.TOOL_USE_START&&(t=n)}return t}function kc(e,t,n,s){if(!e||!t)return[];let r=!1;return t.filter(a=>{var i;const o=s!=null&&s.isActive&&a.tool_use?s.getToolUseState(a.tool_use.tool_use_id):n.getToolUseState(a.requestId??e,(i=a.tool_use)==null?void 0:i.tool_use_id);return r===!1&&o.phase!==M.new&&o.phase!==M.unknown&&o.phase!==M.checkingSafety&&a.tool_use!==void 0||(o.phase===M.runnable&&(r=!0),!1)})}function Fp(e,t){if(e.contentNodes&&e.contentNodes.length>0){const n=e.contentNodes.map(s=>{if(s.type===An.ContentText){let r="";return s.text_content&&(r=Ga(s.text_content,t/e.contentNodes.length)),{...s,text_content:r}}return s});return{...e,contentNodes:n}}return{...e,text:Ga(e.text,t)}}const Cc="__NEW_AGENT__",Up=e=>e.chatItemType===void 0,$p=(e,t)=>{var a;const n=e.chatHistory.at(-1);if(!n||!H(n))return Qe.notRunning;if(!(n.status===C.success||n.status===C.failed||n.status===C.cancelled))return Qe.running;const s=((a=n.structured_output_nodes)==null?void 0:a.filter(o=>o.type===R.TOOL_USE&&!!o.tool_use))??[];let r;if(t.enableParallelTools?(r=kc(n.request_id,s,e).at(-1),!r&&s.length>0&&(r=s.at(-1))):r=s.at(-1),!r||!r.tool_use)return Qe.notRunning;switch(e.getToolUseState(n.request_id,r.tool_use.tool_use_id).phase){case M.runnable:return Qe.awaitingUserAction;case M.cancelled:return Qe.notRunning;default:return Qe.running}},qs=e=>H(e)&&!!e.request_message,xc=e=>e.chatHistory.findLast(t=>qs(t)),qp=(e,t)=>{const n=xc(e);return n!=null&&n.request_id?e.historyFrom(n.request_id,!0).filter(s=>H(s)&&(!t||t(s))):[]},Hp=e=>{var s;const t=e.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!H(t))return!1;const n=((s=t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===R.TOOL_USE))??[];for(const r of n)if(r.tool_use&&e.getToolUseState(t.request_id,r.tool_use.tool_use_id).phase===M.runnable)return e.updateToolUseState({requestId:t.request_id,toolUseId:r.tool_use.tool_use_id,phase:M.cancelled}),!0;return!1};function Ac(e,t){const n=e.customPersonalityPrompts;if(n)switch(t){case z.DEFAULT:if(n.agent&&n.agent.trim()!=="")return n.agent;break;case z.PROTOTYPER:if(n.prototyper&&n.prototyper.trim()!=="")return n.prototyper;break;case z.BRAINSTORM:if(n.brainstorm&&n.brainstorm.trim()!=="")return n.brainstorm;break;case z.REVIEWER:if(n.reviewer&&n.reviewer.trim()!=="")return n.reviewer}return Rc[t]}const Rc={[z.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[z.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[z.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[z.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};var ce=(e=>(e.NOT_STARTED="NOT_STARTED",e.IN_PROGRESS="IN_PROGRESS",e.CANCELLED="CANCELLED",e.COMPLETE="COMPLETE",e))(ce||{}),qr=(e=>(e.USER="USER",e.AGENT="AGENT",e))(qr||{}),Li={},Rn={},Mn={};let nn;Object.defineProperty(Mn,"__esModule",{value:!0}),Mn.default=function(){if(!nn&&(nn=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!nn))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return nn(Mc)};const Mc=new Uint8Array(16);var ze={},st={},On={};Object.defineProperty(On,"__esModule",{value:!0}),On.default=void 0;On.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,Object.defineProperty(st,"__esModule",{value:!0}),st.default=void 0;var sn,Oc=(sn=On)&&sn.__esModule?sn:{default:sn},Pc=function(e){return typeof e=="string"&&Oc.default.test(e)};st.default=Pc,Object.defineProperty(ze,"__esModule",{value:!0}),ze.default=void 0,ze.unsafeStringify=Fi;var Dc=function(e){return e&&e.__esModule?e:{default:e}}(st);const Y=[];for(let e=0;e<256;++e)Y.push((e+256).toString(16).slice(1));function Fi(e,t=0){return Y[e[t+0]]+Y[e[t+1]]+Y[e[t+2]]+Y[e[t+3]]+"-"+Y[e[t+4]]+Y[e[t+5]]+"-"+Y[e[t+6]]+Y[e[t+7]]+"-"+Y[e[t+8]]+Y[e[t+9]]+"-"+Y[e[t+10]]+Y[e[t+11]]+Y[e[t+12]]+Y[e[t+13]]+Y[e[t+14]]+Y[e[t+15]]}var Lc=function(e,t=0){const n=Fi(e,t);if(!(0,Dc.default)(n))throw TypeError("Stringified UUID is invalid");return n};ze.default=Lc,Object.defineProperty(Rn,"__esModule",{value:!0}),Rn.default=void 0;var Fc=function(e){return e&&e.__esModule?e:{default:e}}(Mn),Uc=ze;let Va,us,cs=0,ds=0;var $c=function(e,t,n){let s=t&&n||0;const r=t||new Array(16);let a=(e=e||{}).node||Va,o=e.clockseq!==void 0?e.clockseq:us;if(a==null||o==null){const h=e.random||(e.rng||Fc.default)();a==null&&(a=Va=[1|h[0],h[1],h[2],h[3],h[4],h[5]]),o==null&&(o=us=16383&(h[6]<<8|h[7]))}let i=e.msecs!==void 0?e.msecs:Date.now(),c=e.nsecs!==void 0?e.nsecs:ds+1;const l=i-cs+(c-ds)/1e4;if(l<0&&e.clockseq===void 0&&(o=o+1&16383),(l<0||i>cs)&&e.nsecs===void 0&&(c=0),c>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");cs=i,ds=c,us=o,i+=122192928e5;const d=(1e4*(268435455&i)+c)%4294967296;r[s++]=d>>>24&255,r[s++]=d>>>16&255,r[s++]=d>>>8&255,r[s++]=255&d;const u=i/4294967296*1e4&268435455;r[s++]=u>>>8&255,r[s++]=255&u,r[s++]=u>>>24&15|16,r[s++]=u>>>16&255,r[s++]=o>>>8|128,r[s++]=255&o;for(let h=0;h<6;++h)r[s+h]=a[h];return t||(0,Uc.unsafeStringify)(r)};Rn.default=$c;var Pn={},je={},jt={};Object.defineProperty(jt,"__esModule",{value:!0}),jt.default=void 0;var qc=function(e){return e&&e.__esModule?e:{default:e}}(st),Hc=function(e){if(!(0,qc.default)(e))throw TypeError("Invalid UUID");let t;const n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n};jt.default=Hc,Object.defineProperty(je,"__esModule",{value:!0}),je.URL=je.DNS=void 0,je.default=function(e,t,n){function s(r,a,o,i){var c;if(typeof r=="string"&&(r=function(d){d=unescape(encodeURIComponent(d));const u=[];for(let h=0;h<d.length;++h)u.push(d.charCodeAt(h));return u}(r)),typeof a=="string"&&(a=(0,Gc.default)(a)),((c=a)===null||c===void 0?void 0:c.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let l=new Uint8Array(16+r.length);if(l.set(a),l.set(r,a.length),l=n(l),l[6]=15&l[6]|t,l[8]=63&l[8]|128,o){i=i||0;for(let d=0;d<16;++d)o[i+d]=l[d];return o}return(0,Bc.unsafeStringify)(l)}try{s.name=e}catch{}return s.DNS=Ui,s.URL=$i,s};var Bc=ze,Gc=function(e){return e&&e.__esModule?e:{default:e}}(jt);const Ui="6ba7b810-9dad-11d1-80b4-00c04fd430c8";je.DNS=Ui;const $i="6ba7b811-9dad-11d1-80b4-00c04fd430c8";je.URL=$i;var Dn={};function ja(e){return 14+(e+64>>>9<<4)+1}function Ye(e,t){const n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function Yn(e,t,n,s,r,a){return Ye((o=Ye(Ye(t,e),Ye(s,a)))<<(i=r)|o>>>32-i,n);var o,i}function J(e,t,n,s,r,a,o){return Yn(t&n|~t&s,e,t,r,a,o)}function Z(e,t,n,s,r,a,o){return Yn(t&s|n&~s,e,t,r,a,o)}function Q(e,t,n,s,r,a,o){return Yn(t^n^s,e,t,r,a,o)}function ee(e,t,n,s,r,a,o){return Yn(n^(t|~s),e,t,r,a,o)}Object.defineProperty(Dn,"__esModule",{value:!0}),Dn.default=void 0;var Vc=function(e){if(typeof e=="string"){const t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(let n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(t){const n=[],s=32*t.length,r="0123456789abcdef";for(let a=0;a<s;a+=8){const o=t[a>>5]>>>a%32&255,i=parseInt(r.charAt(o>>>4&15)+r.charAt(15&o),16);n.push(i)}return n}(function(t,n){t[n>>5]|=128<<n%32,t[ja(n)-1]=n;let s=1732584193,r=-271733879,a=-1732584194,o=271733878;for(let i=0;i<t.length;i+=16){const c=s,l=r,d=a,u=o;s=J(s,r,a,o,t[i],7,-680876936),o=J(o,s,r,a,t[i+1],12,-389564586),a=J(a,o,s,r,t[i+2],17,606105819),r=J(r,a,o,s,t[i+3],22,-1044525330),s=J(s,r,a,o,t[i+4],7,-176418897),o=J(o,s,r,a,t[i+5],12,1200080426),a=J(a,o,s,r,t[i+6],17,-1473231341),r=J(r,a,o,s,t[i+7],22,-45705983),s=J(s,r,a,o,t[i+8],7,1770035416),o=J(o,s,r,a,t[i+9],12,-1958414417),a=J(a,o,s,r,t[i+10],17,-42063),r=J(r,a,o,s,t[i+11],22,-1990404162),s=J(s,r,a,o,t[i+12],7,1804603682),o=J(o,s,r,a,t[i+13],12,-40341101),a=J(a,o,s,r,t[i+14],17,-1502002290),r=J(r,a,o,s,t[i+15],22,1236535329),s=Z(s,r,a,o,t[i+1],5,-165796510),o=Z(o,s,r,a,t[i+6],9,-1069501632),a=Z(a,o,s,r,t[i+11],14,643717713),r=Z(r,a,o,s,t[i],20,-373897302),s=Z(s,r,a,o,t[i+5],5,-701558691),o=Z(o,s,r,a,t[i+10],9,38016083),a=Z(a,o,s,r,t[i+15],14,-660478335),r=Z(r,a,o,s,t[i+4],20,-405537848),s=Z(s,r,a,o,t[i+9],5,568446438),o=Z(o,s,r,a,t[i+14],9,-1019803690),a=Z(a,o,s,r,t[i+3],14,-187363961),r=Z(r,a,o,s,t[i+8],20,1163531501),s=Z(s,r,a,o,t[i+13],5,-1444681467),o=Z(o,s,r,a,t[i+2],9,-51403784),a=Z(a,o,s,r,t[i+7],14,1735328473),r=Z(r,a,o,s,t[i+12],20,-1926607734),s=Q(s,r,a,o,t[i+5],4,-378558),o=Q(o,s,r,a,t[i+8],11,-2022574463),a=Q(a,o,s,r,t[i+11],16,1839030562),r=Q(r,a,o,s,t[i+14],23,-35309556),s=Q(s,r,a,o,t[i+1],4,-1530992060),o=Q(o,s,r,a,t[i+4],11,1272893353),a=Q(a,o,s,r,t[i+7],16,-155497632),r=Q(r,a,o,s,t[i+10],23,-1094730640),s=Q(s,r,a,o,t[i+13],4,681279174),o=Q(o,s,r,a,t[i],11,-358537222),a=Q(a,o,s,r,t[i+3],16,-722521979),r=Q(r,a,o,s,t[i+6],23,76029189),s=Q(s,r,a,o,t[i+9],4,-640364487),o=Q(o,s,r,a,t[i+12],11,-421815835),a=Q(a,o,s,r,t[i+15],16,530742520),r=Q(r,a,o,s,t[i+2],23,-995338651),s=ee(s,r,a,o,t[i],6,-198630844),o=ee(o,s,r,a,t[i+7],10,1126891415),a=ee(a,o,s,r,t[i+14],15,-1416354905),r=ee(r,a,o,s,t[i+5],21,-57434055),s=ee(s,r,a,o,t[i+12],6,1700485571),o=ee(o,s,r,a,t[i+3],10,-1894986606),a=ee(a,o,s,r,t[i+10],15,-1051523),r=ee(r,a,o,s,t[i+1],21,-2054922799),s=ee(s,r,a,o,t[i+8],6,1873313359),o=ee(o,s,r,a,t[i+15],10,-30611744),a=ee(a,o,s,r,t[i+6],15,-1560198380),r=ee(r,a,o,s,t[i+13],21,1309151649),s=ee(s,r,a,o,t[i+4],6,-145523070),o=ee(o,s,r,a,t[i+11],10,-1120210379),a=ee(a,o,s,r,t[i+2],15,718787259),r=ee(r,a,o,s,t[i+9],21,-343485551),s=Ye(s,c),r=Ye(r,l),a=Ye(a,d),o=Ye(o,u)}return[s,r,a,o]}(function(t){if(t.length===0)return[];const n=8*t.length,s=new Uint32Array(ja(n));for(let r=0;r<n;r+=8)s[r>>5]|=(255&t[r/8])<<r%32;return s}(e),8*e.length))};Dn.default=Vc,Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.default=void 0;var jc=qi(je),Yc=qi(Dn);function qi(e){return e&&e.__esModule?e:{default:e}}var Kc=(0,jc.default)("v3",48,Yc.default);Pn.default=Kc;var Ln={},Fn={};Object.defineProperty(Fn,"__esModule",{value:!0}),Fn.default=void 0;var Wc={randomUUID:typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};Fn.default=Wc,Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.default=void 0;var Ya=Hi(Fn),zc=Hi(Mn),Xc=ze;function Hi(e){return e&&e.__esModule?e:{default:e}}var Jc=function(e,t,n){if(Ya.default.randomUUID&&!t&&!e)return Ya.default.randomUUID();const s=(e=e||{}).random||(e.rng||zc.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){n=n||0;for(let r=0;r<16;++r)t[n+r]=s[r];return t}return(0,Xc.unsafeStringify)(s)};Ln.default=Jc;var Un={},$n={};function Zc(e,t,n,s){switch(e){case 0:return t&n^~t&s;case 1:case 3:return t^n^s;case 2:return t&n^t&s^n&s}}function hs(e,t){return e<<t|e>>>32-t}Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var Qc=function(e){const t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof e=="string"){const o=unescape(encodeURIComponent(e));e=[];for(let i=0;i<o.length;++i)e.push(o.charCodeAt(i))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);const s=e.length/4+2,r=Math.ceil(s/16),a=new Array(r);for(let o=0;o<r;++o){const i=new Uint32Array(16);for(let c=0;c<16;++c)i[c]=e[64*o+4*c]<<24|e[64*o+4*c+1]<<16|e[64*o+4*c+2]<<8|e[64*o+4*c+3];a[o]=i}a[r-1][14]=8*(e.length-1)/Math.pow(2,32),a[r-1][14]=Math.floor(a[r-1][14]),a[r-1][15]=8*(e.length-1)&4294967295;for(let o=0;o<r;++o){const i=new Uint32Array(80);for(let p=0;p<16;++p)i[p]=a[o][p];for(let p=16;p<80;++p)i[p]=hs(i[p-3]^i[p-8]^i[p-14]^i[p-16],1);let c=n[0],l=n[1],d=n[2],u=n[3],h=n[4];for(let p=0;p<80;++p){const m=Math.floor(p/20),y=hs(c,5)+Zc(m,l,d,u)+h+t[m]+i[p]>>>0;h=u,u=d,d=hs(l,30)>>>0,l=c,c=y}n[0]=n[0]+c>>>0,n[1]=n[1]+l>>>0,n[2]=n[2]+d>>>0,n[3]=n[3]+u>>>0,n[4]=n[4]+h>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]};$n.default=Qc,Object.defineProperty(Un,"__esModule",{value:!0}),Un.default=void 0;var ed=Bi(je),td=Bi($n);function Bi(e){return e&&e.__esModule?e:{default:e}}var nd=(0,ed.default)("v5",80,td.default);Un.default=nd;var qn={};Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;qn.default="00000000-0000-0000-0000-000000000000";var Hn={};Object.defineProperty(Hn,"__esModule",{value:!0}),Hn.default=void 0;var sd=function(e){return e&&e.__esModule?e:{default:e}}(st),rd=function(e){if(!(0,sd.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)};function Hs(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}Hn.default=rd,function(e){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"NIL",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(e,"parse",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(e,"stringify",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(e,"v1",{enumerable:!0,get:function(){return t.default}}),Object.defineProperty(e,"v3",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(e,"v4",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"v5",{enumerable:!0,get:function(){return r.default}}),Object.defineProperty(e,"validate",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"version",{enumerable:!0,get:function(){return o.default}});var t=d(Rn),n=d(Pn),s=d(Ln),r=d(Un),a=d(qn),o=d(Hn),i=d(st),c=d(ze),l=d(jt);function d(u){return u&&u.__esModule?u:{default:u}}}(Li),Hs.prototype.convert=function(e){var t,n,s,r={},a=this.srcAlphabet.length,o=this.dstAlphabet.length,i=e.length,c=typeof e=="string"?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<i;t++)r[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,s=0,t=0;t<i;t++)(n=n*a+r[t])>=o?(r[s++]=parseInt(n/o,10),n%=o):s>0&&(r[s++]=0);i=s,c=this.dstAlphabet.slice(n,n+1).concat(c)}while(s!==0);return c},Hs.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(this.srcAlphabet.indexOf(e[t])===-1)return!1;return!0};var ad=Hs;function At(e,t){var n=new ad(e,t);return function(s){return n.convert(s)}}At.BIN="01",At.OCT="01234567",At.DEC="0123456789",At.HEX="0123456789abcdef";var od=At;const{v4:ps,validate:id}=Li,rn=od,ms={cookieBase90:"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!#$%&'()*+-./:<=>?@[]^_`{|}~",flickrBase58:"123456789abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",uuid25Base36:"0123456789abcdefghijklmnopqrstuvwxyz"},ld={consistentLength:!0};let gs;const Ka=(e,t,n)=>{const s=t(e.toLowerCase().replace(/-/g,""));return n&&n.consistentLength?s.padStart(n.shortIdLength,n.paddingChar):s},Wa=(e,t)=>{const n=t(e).padStart(32,"0").match(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/);return[n[1],n[2],n[3],n[4],n[5]].join("-")};var ud=(()=>{const e=(t,n)=>{const s=t||ms.flickrBase58,r={...ld,...n};if([...new Set(Array.from(s))].length!==s.length)throw new Error("The provided Alphabet has duplicate characters resulting in unreliable results");const a=(o=s.length,Math.ceil(Math.log(2**128)/Math.log(o)));var o;const i={shortIdLength:a,consistentLength:r.consistentLength,paddingChar:s[0]},c=rn(rn.HEX,s),l=rn(s,rn.HEX),d=()=>Ka(ps(),c,i),u={alphabet:s,fromUUID:h=>Ka(h,c,i),maxLength:a,generate:d,new:d,toUUID:h=>Wa(h,l),uuid:ps,validate:(h,p=!1)=>{if(!h||typeof h!="string")return!1;const m=r.consistentLength?h.length===a:h.length<=a,y=h.split("").every(f=>s.includes(f));return p===!1?m&&y:m&&y&&id(Wa(h,l))}};return Object.freeze(u),u};return e.constants=ms,e.uuid=ps,e.generate=()=>(gs||(gs=e(ms.flickrBase58).generate),gs()),e})();const cd=_r(ud),Gi={[ce.NOT_STARTED]:"[ ]",[ce.IN_PROGRESS]:"[/]",[ce.COMPLETE]:"[x]",[ce.CANCELLED]:"[-]"},Vi=cd(void 0,{consistentLength:!0});function dd(e,t){if(e.uuid===t)return e;if(e.subTasksData)for(const n of e.subTasksData){const s=dd(n,t);if(s)return s}}function ji(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:r=!0}=t;return Yi(e,{shallow:n,excludeUuid:s,shortUuid:r}).join(`
`)}function Yi(e,t={}){const{shallow:n=!1,excludeUuid:s=!1,shortUuid:r=!0}=t;let a="";s||(a=`UUID:${r?function(i){try{return Vi.fromUUID(i)}catch{return i}}(e.uuid):e.uuid} `);const o=`${Gi[e.state]} ${a}NAME:${e.name} DESCRIPTION:${e.description}`;return n||!e.subTasksData||e.subTasksData.length===0?[o]:[o,...(e.subTasksData||[]).map(i=>Yi(i,t).map(c=>`-${c}`)).flat()]}function hd(e,t){var s;const n=(s=e.subTasksData)==null?void 0:s.map(r=>hd(r,t));return{...e,uuid:t!=null&&t.keepUuid?e.uuid:crypto.randomUUID(),subTasks:(n==null?void 0:n.map(r=>r.uuid))||[],subTasksData:n}}function Bp(e,t={}){if(!e.trim())throw new Error("Empty markdown");const n=e.split(`
`);let s=0;for(const l of n)if(l.trim()&&za(l)===0)try{Bs(l,t),s++}catch{}if(s===0)throw new Error("No root task found");if(s>1)throw new Error(`Multiple root tasks found (${s}). There can only be one root task per conversation. All other tasks must be subtasks (indented with dashes). Root task format: [ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (no dashes). Subtask format: -[ ] UUID:xxx NAME:yyy DESCRIPTION:zzz (with dashes).`);const r=e.split(`
`);function a(){for(;r.length>0;){const l=r.shift(),d=za(l);try{return{task:Bs(l,t),level:d}}catch{}}}const o=a();if(!o)throw new Error("No root task found");const i=[o.task];let c;for(;c=a();){const l=i[c.level-1];if(!l)throw new Error(`Invalid markdown: level ${c.level+1} has no parent
Line: ${c.task.name} is missing a parent
Current tasks: 
${ji(o.task)}`);l.subTasksData&&l.subTasks||(l.subTasks=[],l.subTasksData=[]),l.subTasksData.push(c.task),l.subTasks.push(c.task.uuid),i[c.level]=c.task,i.splice(c.level+1)}return o.task}function za(e){let t=0,n=0;for(;n<e.length&&(e[n]===" "||e[n]==="	");)e[n]===" "?t+=.5:e[n]==="	"&&(t+=1),n++;for(;n<e.length&&e[n]==="-";)t+=1,n++;return Math.floor(t)}function Bs(e,t={}){const{excludeUuid:n=!1,shortUuid:s=!0}=t;let r=0;for(;r<e.length&&(e[r]===" "||e[r]==="	"||e[r]==="-");)r++;const a=e.substring(r),o=a.match(/^\s*\[([ x\-/?])\]/);if(!o)throw new Error(`Invalid task line: ${e} (missing state)`);const i=o[1],c=Object.entries(Gi).reduce((p,[m,y])=>(p[y.substring(1,2)]=m,p),{})[i]||ce.NOT_STARTED,l=a.substring(o.index+o[0].length).trim();let d,u,h;if(n){const p=/(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(p);if(!m){const y=/\b(?:name|NAME):/i.test(l),f=/\b(?:description|DESCRIPTION):/i.test(l);throw!y||!f?new Error(`Invalid task line: ${e} (missing required fields)`):l.toLowerCase().indexOf("name:")<l.toLowerCase().indexOf("description:")?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(u=m[1].trim(),h=m[2].trim(),!u)throw new Error(`Invalid task line: ${e} (missing required fields)`);d=crypto.randomUUID()}else{const p=/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i,m=l.match(p);if(!m){const y=/\b(?:uuid|UUID):/i.test(l),f=/\b(?:name|NAME):/i.test(l),b=/\b(?:description|DESCRIPTION):/i.test(l);if(!y||!f||!b)throw new Error(`Invalid task line: ${e} (missing required fields)`);const S=l.toLowerCase().indexOf("uuid:"),v=l.toLowerCase().indexOf("name:"),T=l.toLowerCase().indexOf("description:");throw S<v&&v<T?new Error(`Invalid task line: ${e} (invalid format)`):new Error(`Invalid task line: ${e} (incorrect field order)`)}if(d=m[1].trim(),u=m[2].trim(),h=m[3].trim(),!d||!u)throw new Error(`Invalid task line: ${e} (missing required fields)`);if(d==="NEW_UUID")d=crypto.randomUUID();else if(s)try{d=function(y){try{return Vi.toUUID(y)}catch{return y}}(d)}catch{}}return{uuid:d,name:u,description:h,state:c,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:qr.USER}}const St=e=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:ce.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:qr.USER,...e}),Xa=St({name:"Task 1.1",description:"This is the first sub task",state:ce.IN_PROGRESS}),Ja=St({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:ce.NOT_STARTED}),Za=St({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:ce.IN_PROGRESS}),Qa=St({name:"Task 1.2",description:"This is the second sub task",state:ce.COMPLETE,subTasks:[Ja.uuid,Za.uuid],subTasksData:[Ja,Za]}),eo=St({name:"Task 1.3",description:"This is the third sub task",state:ce.CANCELLED}),Gp=ji(St({name:"Task 1",description:"This is the first task",state:ce.NOT_STARTED,subTasks:[Xa.uuid,Qa.uuid,eo.uuid],subTasksData:[Xa,Qa,eo]}));function Ki(e){const t=e.split(`
`);let n=null;const s={created:[],updated:[],deleted:[]};for(const r of t){const a=r.trim();if(a!=="## Created Tasks")if(a!=="## Updated Tasks")if(a!=="## Deleted Tasks"){if(n&&(a.startsWith("[ ]")||a.startsWith("[/]")||a.startsWith("[x]")||a.startsWith("[-]")))try{const o=Bs(a,{excludeUuid:!1,shortUuid:!0});o&&s[n].push(o)}catch{}}else n="deleted";else n="updated";else n="created"}return s}function Vp(e){const t=e.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const n=Ki(Wi(e));return{created:n.created.length,updated:n.updated.length,deleted:n.deleted.length}}function Wi(e){const t=e.indexOf("# Task Changes");if(t===-1)return"";const n=e.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let r=n.length;for(const i of s){const c=n.indexOf(i);c!==-1&&c<r&&(r=c)}const a=n.substring(0,r),o=a.indexOf(`
`);return o===-1?"":a.substring(o+1).trim()}function jp(e){return Ki(Wi(e))}class pd{static getTaskOrchestratorPrompt(t){const{taskTree:n,surroundingContext:s}=t,r=this.buildTaskContext(n,s);return`Please utilize sub-agents to complete the following task tree.
Here are the details, along with a suggestion prompt.
You may use 1 or more sub-agents in to complete the below task.
For each sub-agent, please give it the relevant context and breakdown of the below task.

## Task Details
**Name:** ${n.name}
${n.description?`**Description:** ${n.description}`:""}
**Status:** ${n.state}

## Task Context
${r}

## Instructions
Please complete this task according to the requirements.
When you are done, report back on the completion status with a summary of changes made,
important context, and other relevant information for the supervisor.

Focus on this specific task tree while being aware of the broader context provided above.`}static getTaskMentionId(t){return`task:${t.taskUuid}:${t.taskTree.name.replace(/\s+/g,"_")}`}static getTaskMentionLabel(t){const{taskTree:n,surroundingContext:s}=t;return s.targetTaskPath.length>1?`${s.targetTaskPath.slice(0,-1).join(" → ")} → ${n.name}`:n.name}static buildTaskContext(t,n){const{rootTask:s,targetTaskPath:r}=n;let a=`This task is part of a larger project: "${s.name}"`;return s.description&&(a+=`

**Project Description:** ${s.description}`),r.length>1&&(a+=`

**Task Path:** ${r.join(" → ")}`),t.subTasksData&&t.subTasksData.length>0&&(a+=`

**Subtasks:**`,t.subTasksData.forEach((o,i)=>{a+=`
${i+1}. ${o.name} (${o.state})`,o.description&&(a+=` - ${o.description}`)})),a}}function Tt(e){var t;return((t=e.extraData)==null?void 0:t.isAgentConversation)===!0}var md=(e=>(e[e.active=0]="active",e[e.inactive=1]="inactive",e))(md||{});const q={triggerOnHistorySizeChars:0,historyTailSizeCharsToExclude:0,triggerOnHistorySizeCharsWhenCacheExpiring:0,prompt:"",cacheTTLMs:0,bufferTimeBeforeCacheExpirationMs:0,summaryNodeRequestMessageTemplate:`
<supervisor>
Conversation history between Agent(you) and the user and history of tool calls was abridged and summarized to reduce context size.
Abridged conversation history:
{abridged_history}

Summary was generated by Agent(you) so 'I' in the summary represents Agent(you).
Here is the summary:
{summary}

Continue the conversation and finish the task given by the user from this point.
</supervisor>`,summaryNodeResponseMessage:"Ok. I will continue the conversation from this point.",abridgedHistoryParams:{totalCharsLimit:1e4,userMessageCharsLimit:1e3,agentResponseCharsLimit:2e3,actionCharsLimit:200,numFilesModifiedLimit:10,numFilesCreatedLimit:10,numFilesDeletedLimit:10,numFilesViewedLimit:10,numTerminalCommandsLimit:10}};function Gs(e,t,n=.5,s=.5){if(e.length<=t||t<=0)return e;if(n+s>1)throw new Error("startRatio + endRatio cannot exceed 1.0");const r="...",a=t-3;if(a<=0)return r.substring(0,t);const o=Math.floor(a*n),i=Math.floor(a*s);return e.substring(0,o)+r+e.substring(e.length-i)}const V=(e,t)=>e!==void 0?e:t;var Vs={exports:{}},js={exports:{}},ge={},P={__esModule:!0};P.extend=to,P.indexOf=function(e,t){for(var n=0,s=e.length;n<s;n++)if(e[n]===t)return n;return-1},P.escapeExpression=function(e){if(typeof e!="string"){if(e&&e.toHTML)return e.toHTML();if(e==null)return"";if(!e)return e+"";e=""+e}return yd.test(e)?e.replace(fd,_d):e},P.isEmpty=function(e){return!e&&e!==0||!(!zi(e)||e.length!==0)},P.createFrame=function(e){var t=to({},e);return t._parent=e,t},P.blockParams=function(e,t){return e.path=t,e},P.appendContextPath=function(e,t){return(e?e+".":"")+t};var gd={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},fd=/[&<>"'`=]/g,yd=/[&<>"'`=]/;function _d(e){return gd[e]}function to(e){for(var t=1;t<arguments.length;t++)for(var n in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],n)&&(e[n]=arguments[t][n]);return e}var Hr=Object.prototype.toString;P.toString=Hr;var fs=function(e){return typeof e=="function"};fs(/x/)&&(P.isFunction=fs=function(e){return typeof e=="function"&&Hr.call(e)==="[object Function]"}),P.isFunction=fs;var zi=Array.isArray||function(e){return!(!e||typeof e!="object")&&Hr.call(e)==="[object Array]"};P.isArray=zi;var Ys={exports:{}};(function(e,t){t.__esModule=!0;var n=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];function s(r,a){var o=a&&a.loc,i=void 0,c=void 0,l=void 0,d=void 0;o&&(i=o.start.line,c=o.end.line,l=o.start.column,d=o.end.column,r+=" - "+i+":"+l);for(var u=Error.prototype.constructor.call(this,r),h=0;h<n.length;h++)this[n[h]]=u[n[h]];Error.captureStackTrace&&Error.captureStackTrace(this,s);try{o&&(this.lineNumber=i,this.endLineNumber=c,Object.defineProperty?(Object.defineProperty(this,"column",{value:l,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:d,enumerable:!0})):(this.column=l,this.endColumn=d))}catch{}}s.prototype=new Error,t.default=s,e.exports=t.default})(Ys,Ys.exports);var Te=Ys.exports,Lt={},Ks={exports:{}};(function(e,t){t.__esModule=!0;var n=P;t.default=function(s){s.registerHelper("blockHelperMissing",function(r,a){var o=a.inverse,i=a.fn;if(r===!0)return i(this);if(r===!1||r==null)return o(this);if(n.isArray(r))return r.length>0?(a.ids&&(a.ids=[a.name]),s.helpers.each(r,a)):o(this);if(a.data&&a.ids){var c=n.createFrame(a.data);c.contextPath=n.appendContextPath(a.data.contextPath,a.name),a={data:c}}return i(r,a)})},e.exports=t.default})(Ks,Ks.exports);var bd=Ks.exports,Ws={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s=function(r){return r&&r.__esModule?r:{default:r}}(Te);t.default=function(r){r.registerHelper("each",function(a,o){if(!o)throw new s.default("Must pass iterator to #each");var i,c=o.fn,l=o.inverse,d=0,u="",h=void 0,p=void 0;function m(v,T,w){h&&(h.key=v,h.index=T,h.first=T===0,h.last=!!w,p&&(h.contextPath=p+v)),u+=c(a[v],{data:h,blockParams:n.blockParams([a[v],v],[p+v,null])})}if(o.data&&o.ids&&(p=n.appendContextPath(o.data.contextPath,o.ids[0])+"."),n.isFunction(a)&&(a=a.call(this)),o.data&&(h=n.createFrame(o.data)),a&&typeof a=="object")if(n.isArray(a))for(var y=a.length;d<y;d++)d in a&&m(d,d,d===a.length-1);else if(typeof Symbol=="function"&&a[Symbol.iterator]){for(var f=[],b=a[Symbol.iterator](),S=b.next();!S.done;S=b.next())f.push(S.value);for(y=(a=f).length;d<y;d++)m(d,d,d===a.length-1)}else i=void 0,Object.keys(a).forEach(function(v){i!==void 0&&m(i,d-1),i=v,d++}),i!==void 0&&m(i,d-1,!0);return d===0&&(u=l(this)),u})},e.exports=t.default})(Ws,Ws.exports);var vd=Ws.exports,zs={exports:{}};(function(e,t){t.__esModule=!0;var n=function(s){return s&&s.__esModule?s:{default:s}}(Te);t.default=function(s){s.registerHelper("helperMissing",function(){if(arguments.length!==1)throw new n.default('Missing helper: "'+arguments[arguments.length-1].name+'"')})},e.exports=t.default})(zs,zs.exports);var Sd=zs.exports,Xs={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s=function(r){return r&&r.__esModule?r:{default:r}}(Te);t.default=function(r){r.registerHelper("if",function(a,o){if(arguments.length!=2)throw new s.default("#if requires exactly one argument");return n.isFunction(a)&&(a=a.call(this)),!o.hash.includeZero&&!a||n.isEmpty(a)?o.inverse(this):o.fn(this)}),r.registerHelper("unless",function(a,o){if(arguments.length!=2)throw new s.default("#unless requires exactly one argument");return r.helpers.if.call(this,a,{fn:o.inverse,inverse:o.fn,hash:o.hash})})},e.exports=t.default})(Xs,Xs.exports);var no,ys,Ed=Xs.exports,Js={exports:{}};no=Js,(ys=Js.exports).__esModule=!0,ys.default=function(e){e.registerHelper("log",function(){for(var t=[void 0],n=arguments[arguments.length-1],s=0;s<arguments.length-1;s++)t.push(arguments[s]);var r=1;n.hash.level!=null?r=n.hash.level:n.data&&n.data.level!=null&&(r=n.data.level),t[0]=r,e.log.apply(e,t)})},no.exports=ys.default;var Td=Js.exports,Zs={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){n.registerHelper("lookup",function(s,r,a){return s&&a.lookupProperty(s,r)})},e.exports=t.default})(Zs,Zs.exports);var wd=Zs.exports,Qs={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s=function(r){return r&&r.__esModule?r:{default:r}}(Te);t.default=function(r){r.registerHelper("with",function(a,o){if(arguments.length!=2)throw new s.default("#with requires exactly one argument");n.isFunction(a)&&(a=a.call(this));var i=o.fn;if(n.isEmpty(a))return o.inverse(this);var c=o.data;return o.data&&o.ids&&((c=n.createFrame(o.data)).contextPath=n.appendContextPath(o.data.contextPath,o.ids[0])),i(a,{data:c,blockParams:n.blockParams([a],[c&&c.contextPath])})})},e.exports=t.default})(Qs,Qs.exports);var Id=Qs.exports;function rt(e){return e&&e.__esModule?e:{default:e}}Lt.__esModule=!0,Lt.registerDefaultHelpers=function(e){Nd.default(e),kd.default(e),Cd.default(e),xd.default(e),Ad.default(e),Rd.default(e),Md.default(e)},Lt.moveHelperToHooks=function(e,t,n){e.helpers[t]&&(e.hooks[t]=e.helpers[t],n||delete e.helpers[t])};var Nd=rt(bd),kd=rt(vd),Cd=rt(Sd),xd=rt(Ed),Ad=rt(Td),Rd=rt(wd),Md=rt(Id),er={},tr={exports:{}};(function(e,t){t.__esModule=!0;var n=P;t.default=function(s){s.registerDecorator("inline",function(r,a,o,i){var c=r;return a.partials||(a.partials={},c=function(l,d){var u=o.partials;o.partials=n.extend({},u,a.partials);var h=r(l,d);return o.partials=u,h}),a.partials[i.args[0]]=i.fn,c})},e.exports=t.default})(tr,tr.exports);var Od=tr.exports;er.__esModule=!0,er.registerDefaultDecorators=function(e){Pd.default(e)};var Pd=function(e){return e&&e.__esModule?e:{default:e}}(Od),nr={exports:{}};(function(e,t){t.__esModule=!0;var n=P,s={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(r){if(typeof r=="string"){var a=n.indexOf(s.methodMap,r.toLowerCase());r=a>=0?a:parseInt(r,10)}return r},log:function(r){if(r=s.lookupLevel(r),typeof console<"u"&&s.lookupLevel(s.level)<=r){var a=s.methodMap[r];console[a]||(a="log");for(var o=arguments.length,i=Array(o>1?o-1:0),c=1;c<o;c++)i[c-1]=arguments[c];console[a].apply(console,i)}}};t.default=s,e.exports=t.default})(nr,nr.exports);var Xi=nr.exports,dt={},Dd={__esModule:!0,createNewLookupObject:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return Ld.extend.apply(void 0,[Object.create(null)].concat(t))}},Ld=P;dt.__esModule=!0,dt.createProtoAccessControl=function(e){var t=Object.create(null);t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1;var n=Object.create(null);return n.__proto__=!1,{properties:{whitelist:so.createNewLookupObject(n,e.allowedProtoProperties),defaultValue:e.allowProtoPropertiesByDefault},methods:{whitelist:so.createNewLookupObject(t,e.allowedProtoMethods),defaultValue:e.allowProtoMethodsByDefault}}},dt.resultIsAllowed=function(e,t,n){return Ud(typeof e=="function"?t.methods:t.properties,n)},dt.resetLoggedProperties=function(){Object.keys(Bn).forEach(function(e){delete Bn[e]})};var so=Dd,Fd=function(e){return e&&e.__esModule?e:{default:e}}(Xi),Bn=Object.create(null);function Ud(e,t){return e.whitelist[t]!==void 0?e.whitelist[t]===!0:e.defaultValue!==void 0?e.defaultValue:(function(n){Bn[n]!==!0&&(Bn[n]=!0,Fd.default.log("error",'Handlebars: Access has been denied to resolve the property "'+n+`" because it is not an "own property" of its parent.
You can add a runtime option to disable the check or this warning:
See https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details`))}(t),!1)}function Ji(e){return e&&e.__esModule?e:{default:e}}ge.__esModule=!0,ge.HandlebarsEnvironment=sr;var Ze=P,_s=Ji(Te),$d=Lt,qd=er,Gn=Ji(Xi),Hd=dt;ge.VERSION="4.7.8";ge.COMPILER_REVISION=8;ge.LAST_COMPATIBLE_COMPILER_REVISION=7;ge.REVISION_CHANGES={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};var bs="[object Object]";function sr(e,t,n){this.helpers=e||{},this.partials=t||{},this.decorators=n||{},$d.registerDefaultHelpers(this),qd.registerDefaultDecorators(this)}sr.prototype={constructor:sr,logger:Gn.default,log:Gn.default.log,registerHelper:function(e,t){if(Ze.toString.call(e)===bs){if(t)throw new _s.default("Arg not supported with multiple helpers");Ze.extend(this.helpers,e)}else this.helpers[e]=t},unregisterHelper:function(e){delete this.helpers[e]},registerPartial:function(e,t){if(Ze.toString.call(e)===bs)Ze.extend(this.partials,e);else{if(t===void 0)throw new _s.default('Attempting to register a partial called "'+e+'" as undefined');this.partials[e]=t}},unregisterPartial:function(e){delete this.partials[e]},registerDecorator:function(e,t){if(Ze.toString.call(e)===bs){if(t)throw new _s.default("Arg not supported with multiple decorators");Ze.extend(this.decorators,e)}else this.decorators[e]=t},unregisterDecorator:function(e){delete this.decorators[e]},resetLoggedPropertyAccesses:function(){Hd.resetLoggedProperties()}};var Bd=Gn.default.log;ge.log=Bd,ge.createFrame=Ze.createFrame,ge.logger=Gn.default;var rr={exports:{}};(function(e,t){function n(s){this.string=s}t.__esModule=!0,n.prototype.toString=n.prototype.toHTML=function(){return""+this.string},t.default=n,e.exports=t.default})(rr,rr.exports);var Gd=rr.exports,He={},ar={};ar.__esModule=!0,ar.wrapHelper=function(e,t){return typeof e!="function"?e:function(){return arguments[arguments.length-1]=t(arguments[arguments.length-1]),e.apply(this,arguments)}},He.__esModule=!0,He.checkRevision=function(e){var t=e&&e[0]||1,n=De.COMPILER_REVISION;if(!(t>=De.LAST_COMPATIBLE_COMPILER_REVISION&&t<=De.COMPILER_REVISION)){if(t<De.LAST_COMPATIBLE_COMPILER_REVISION){var s=De.REVISION_CHANGES[n],r=De.REVISION_CHANGES[t];throw new Pe.default("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+s+") or downgrade your runtime to an older version ("+r+").")}throw new Pe.default("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+e[1]+").")}},He.template=function(e,t){if(!t)throw new Pe.default("No environment passed to template");if(!e||!e.main)throw new Pe.default("Unknown template object: "+typeof e);e.main.decorator=e.main_d,t.VM.checkRevision(e.compiler);var n=e.compiler&&e.compiler[0]===7,s={strict:function(a,o,i){if(!a||!(o in a))throw new Pe.default('"'+o+'" not defined in '+a,{loc:i});return s.lookupProperty(a,o)},lookupProperty:function(a,o){var i=a[o];return i==null||Object.prototype.hasOwnProperty.call(a,o)||ao.resultIsAllowed(i,s.protoAccessControl,o)?i:void 0},lookup:function(a,o){for(var i=a.length,c=0;c<i;c++)if((a[c]&&s.lookupProperty(a[c],o))!=null)return a[c][o]},lambda:function(a,o){return typeof a=="function"?a.call(o):a},escapeExpression:Fe.escapeExpression,invokePartial:function(a,o,i){i.hash&&(o=Fe.extend({},o,i.hash),i.ids&&(i.ids[0]=!0)),a=t.VM.resolvePartial.call(this,a,o,i);var c=Fe.extend({},i,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),l=t.VM.invokePartial.call(this,a,o,c);if(l==null&&t.compile&&(i.partials[i.name]=t.compile(a,e.compilerOptions,t),l=i.partials[i.name](o,c)),l!=null){if(i.indent){for(var d=l.split(`
`),u=0,h=d.length;u<h&&(d[u]||u+1!==h);u++)d[u]=i.indent+d[u];l=d.join(`
`)}return l}throw new Pe.default("The partial "+i.name+" could not be compiled when running in runtime-only mode")},fn:function(a){var o=e[a];return o.decorator=e[a+"_d"],o},programs:[],program:function(a,o,i,c,l){var d=this.programs[a],u=this.fn(a);return o||l||c||i?d=an(this,a,u,o,i,c,l):d||(d=this.programs[a]=an(this,a,u)),d},data:function(a,o){for(;a&&o--;)a=a._parent;return a},mergeIfNeeded:function(a,o){var i=a||o;return a&&o&&a!==o&&(i=Fe.extend({},o,a)),i},nullContext:Object.seal({}),noop:t.VM.noop,compilerInfo:e.compiler};function r(a){var o=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],i=o.data;r._setup(o),!o.partial&&e.useData&&(i=function(u,h){return h&&"root"in h||((h=h?De.createFrame(h):{}).root=u),h}(a,i));var c=void 0,l=e.useBlockParams?[]:void 0;function d(u){return""+e.main(s,u,s.helpers,s.partials,i,l,c)}return e.useDepths&&(c=o.depths?a!=o.depths[0]?[a].concat(o.depths):o.depths:[a]),(d=Zi(e.main,d,s,o.depths||[],i,l))(a,o)}return r.isTop=!0,r._setup=function(a){if(a.partial)s.protoAccessControl=a.protoAccessControl,s.helpers=a.helpers,s.partials=a.partials,s.decorators=a.decorators,s.hooks=a.hooks;else{var o=Fe.extend({},t.helpers,a.helpers);(function(c,l){Object.keys(c).forEach(function(d){var u=c[d];c[d]=function(h,p){var m=p.lookupProperty;return Vd.wrapHelper(h,function(y){return Fe.extend({lookupProperty:m},y)})}(u,l)})})(o,s),s.helpers=o,e.usePartial&&(s.partials=s.mergeIfNeeded(a.partials,t.partials)),(e.usePartial||e.useDecorators)&&(s.decorators=Fe.extend({},t.decorators,a.decorators)),s.hooks={},s.protoAccessControl=ao.createProtoAccessControl(a);var i=a.allowCallsToHelperMissing||n;ro.moveHelperToHooks(s,"helperMissing",i),ro.moveHelperToHooks(s,"blockHelperMissing",i)}},r._child=function(a,o,i,c){if(e.useBlockParams&&!i)throw new Pe.default("must pass block params");if(e.useDepths&&!c)throw new Pe.default("must pass parent depths");return an(s,a,e[a],o,0,i,c)},r},He.wrapProgram=an,He.resolvePartial=function(e,t,n){return e?e.call||n.name||(n.name=e,e=n.partials[e]):e=n.name==="@partial-block"?n.data["partial-block"]:n.partials[n.name],e},He.invokePartial=function(e,t,n){var s=n.data&&n.data["partial-block"];n.partial=!0,n.ids&&(n.data.contextPath=n.ids[0]||n.data.contextPath);var r=void 0;if(n.fn&&n.fn!==oo&&function(){n.data=De.createFrame(n.data);var a=n.fn;r=n.data["partial-block"]=function(o){var i=arguments.length<=1||arguments[1]===void 0?{}:arguments[1];return i.data=De.createFrame(i.data),i.data["partial-block"]=s,a(o,i)},a.partials&&(n.partials=Fe.extend({},n.partials,a.partials))}(),e===void 0&&r&&(e=r),e===void 0)throw new Pe.default("The partial "+n.name+" could not be found");if(e instanceof Function)return e(t,n)},He.noop=oo;var Fe=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(P),Pe=function(e){return e&&e.__esModule?e:{default:e}}(Te),De=ge,ro=Lt,Vd=ar,ao=dt;function an(e,t,n,s,r,a,o){function i(c){var l=arguments.length<=1||arguments[1]===void 0?{}:arguments[1],d=o;return!o||c==o[0]||c===e.nullContext&&o[0]===null||(d=[c].concat(o)),n(e,c,e.helpers,e.partials,l.data||s,a&&[l.blockParams].concat(a),d)}return(i=Zi(n,i,e,o,s,a)).program=t,i.depth=o?o.length:0,i.blockParams=r||0,i}function oo(){return""}function Zi(e,t,n,s,r,a){if(e.decorator){var o={};t=e.decorator(t,o,n,s&&s[0],r,a,s),Fe.extend(t,o)}return t}var or={exports:{}};(function(e,t){t.__esModule=!0,t.default=function(n){typeof globalThis!="object"&&(Object.prototype.__defineGetter__("__magic__",function(){return this}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__);var s=globalThis.Handlebars;n.noConflict=function(){return globalThis.Handlebars===n&&(globalThis.Handlebars=s),n}},e.exports=t.default})(or,or.exports);var Qi=or.exports;(function(e,t){function n(h){return h&&h.__esModule?h:{default:h}}function s(h){if(h&&h.__esModule)return h;var p={};if(h!=null)for(var m in h)Object.prototype.hasOwnProperty.call(h,m)&&(p[m]=h[m]);return p.default=h,p}t.__esModule=!0;var r=s(ge),a=n(Gd),o=n(Te),i=s(P),c=s(He),l=n(Qi);function d(){var h=new r.HandlebarsEnvironment;return i.extend(h,r),h.SafeString=a.default,h.Exception=o.default,h.Utils=i,h.escapeExpression=i.escapeExpression,h.VM=c,h.template=function(p){return c.template(p,h)},h}var u=d();u.create=d,l.default(u),u.default=u,t.default=u,e.exports=t.default})(js,js.exports);var jd=js.exports,ir={exports:{}};(function(e,t){t.__esModule=!0;var n={helpers:{helperExpression:function(s){return s.type==="SubExpression"||(s.type==="MustacheStatement"||s.type==="BlockStatement")&&!!(s.params&&s.params.length||s.hash)},scopedId:function(s){return/^\.|this\b/.test(s.original)},simpleId:function(s){return s.parts.length===1&&!n.helpers.scopedId(s)&&!s.depth}}};t.default=n,e.exports=t.default})(ir,ir.exports);var el=ir.exports,Ft={},lr={exports:{}};(function(e,t){t.__esModule=!0;var n=function(){var s={trace:function(){},yy:{},symbols_:{error:2,root:3,program:4,EOF:5,program_repetition0:6,statement:7,mustache:8,block:9,rawBlock:10,partial:11,partialBlock:12,content:13,COMMENT:14,CONTENT:15,openRawBlock:16,rawBlock_repetition0:17,END_RAW_BLOCK:18,OPEN_RAW_BLOCK:19,helperName:20,openRawBlock_repetition0:21,openRawBlock_option0:22,CLOSE_RAW_BLOCK:23,openBlock:24,block_option0:25,closeBlock:26,openInverse:27,block_option1:28,OPEN_BLOCK:29,openBlock_repetition0:30,openBlock_option0:31,openBlock_option1:32,CLOSE:33,OPEN_INVERSE:34,openInverse_repetition0:35,openInverse_option0:36,openInverse_option1:37,openInverseChain:38,OPEN_INVERSE_CHAIN:39,openInverseChain_repetition0:40,openInverseChain_option0:41,openInverseChain_option1:42,inverseAndProgram:43,INVERSE:44,inverseChain:45,inverseChain_option0:46,OPEN_ENDBLOCK:47,OPEN:48,mustache_repetition0:49,mustache_option0:50,OPEN_UNESCAPED:51,mustache_repetition1:52,mustache_option1:53,CLOSE_UNESCAPED:54,OPEN_PARTIAL:55,partialName:56,partial_repetition0:57,partial_option0:58,openPartialBlock:59,OPEN_PARTIAL_BLOCK:60,openPartialBlock_repetition0:61,openPartialBlock_option0:62,param:63,sexpr:64,OPEN_SEXPR:65,sexpr_repetition0:66,sexpr_option0:67,CLOSE_SEXPR:68,hash:69,hash_repetition_plus0:70,hashSegment:71,ID:72,EQUALS:73,blockParams:74,OPEN_BLOCK_PARAMS:75,blockParams_repetition_plus0:76,CLOSE_BLOCK_PARAMS:77,path:78,dataName:79,STRING:80,NUMBER:81,BOOLEAN:82,UNDEFINED:83,NULL:84,DATA:85,pathSegments:86,SEP:87,$accept:0,$end:1},terminals_:{2:"error",5:"EOF",14:"COMMENT",15:"CONTENT",18:"END_RAW_BLOCK",19:"OPEN_RAW_BLOCK",23:"CLOSE_RAW_BLOCK",29:"OPEN_BLOCK",33:"CLOSE",34:"OPEN_INVERSE",39:"OPEN_INVERSE_CHAIN",44:"INVERSE",47:"OPEN_ENDBLOCK",48:"OPEN",51:"OPEN_UNESCAPED",54:"CLOSE_UNESCAPED",55:"OPEN_PARTIAL",60:"OPEN_PARTIAL_BLOCK",65:"OPEN_SEXPR",68:"CLOSE_SEXPR",72:"ID",73:"EQUALS",75:"OPEN_BLOCK_PARAMS",77:"CLOSE_BLOCK_PARAMS",80:"STRING",81:"NUMBER",82:"BOOLEAN",83:"UNDEFINED",84:"NULL",85:"DATA",87:"SEP"},productions_:[0,[3,2],[4,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[7,1],[13,1],[10,3],[16,5],[9,4],[9,4],[24,6],[27,6],[38,6],[43,2],[45,3],[45,1],[26,3],[8,5],[8,5],[11,5],[12,3],[59,5],[63,1],[63,1],[64,5],[69,1],[71,3],[74,3],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[20,1],[56,1],[56,1],[79,2],[78,1],[86,3],[86,1],[6,0],[6,2],[17,0],[17,2],[21,0],[21,2],[22,0],[22,1],[25,0],[25,1],[28,0],[28,1],[30,0],[30,2],[31,0],[31,1],[32,0],[32,1],[35,0],[35,2],[36,0],[36,1],[37,0],[37,1],[40,0],[40,2],[41,0],[41,1],[42,0],[42,1],[46,0],[46,1],[49,0],[49,2],[50,0],[50,1],[52,0],[52,2],[53,0],[53,1],[57,0],[57,2],[58,0],[58,1],[61,0],[61,2],[62,0],[62,1],[66,0],[66,2],[67,0],[67,1],[70,1],[70,2],[76,1],[76,2]],performAction:function(o,i,c,l,d,u,h){var p=u.length-1;switch(d){case 1:return u[p-1];case 2:this.$=l.prepareProgram(u[p]);break;case 3:case 4:case 5:case 6:case 7:case 8:case 20:case 27:case 28:case 33:case 34:case 40:case 41:this.$=u[p];break;case 9:this.$={type:"CommentStatement",value:l.stripComment(u[p]),strip:l.stripFlags(u[p],u[p]),loc:l.locInfo(this._$)};break;case 10:this.$={type:"ContentStatement",original:u[p],value:u[p],loc:l.locInfo(this._$)};break;case 11:this.$=l.prepareRawBlock(u[p-2],u[p-1],u[p],this._$);break;case 12:this.$={path:u[p-3],params:u[p-2],hash:u[p-1]};break;case 13:this.$=l.prepareBlock(u[p-3],u[p-2],u[p-1],u[p],!1,this._$);break;case 14:this.$=l.prepareBlock(u[p-3],u[p-2],u[p-1],u[p],!0,this._$);break;case 15:this.$={open:u[p-5],path:u[p-4],params:u[p-3],hash:u[p-2],blockParams:u[p-1],strip:l.stripFlags(u[p-5],u[p])};break;case 16:case 17:this.$={path:u[p-4],params:u[p-3],hash:u[p-2],blockParams:u[p-1],strip:l.stripFlags(u[p-5],u[p])};break;case 18:this.$={strip:l.stripFlags(u[p-1],u[p-1]),program:u[p]};break;case 19:var m=l.prepareBlock(u[p-2],u[p-1],u[p],u[p],!1,this._$),y=l.prepareProgram([m],u[p-1].loc);y.chained=!0,this.$={strip:u[p-2].strip,program:y,chain:!0};break;case 21:this.$={path:u[p-1],strip:l.stripFlags(u[p-2],u[p])};break;case 22:case 23:this.$=l.prepareMustache(u[p-3],u[p-2],u[p-1],u[p-4],l.stripFlags(u[p-4],u[p]),this._$);break;case 24:this.$={type:"PartialStatement",name:u[p-3],params:u[p-2],hash:u[p-1],indent:"",strip:l.stripFlags(u[p-4],u[p]),loc:l.locInfo(this._$)};break;case 25:this.$=l.preparePartialBlock(u[p-2],u[p-1],u[p],this._$);break;case 26:this.$={path:u[p-3],params:u[p-2],hash:u[p-1],strip:l.stripFlags(u[p-4],u[p])};break;case 29:this.$={type:"SubExpression",path:u[p-3],params:u[p-2],hash:u[p-1],loc:l.locInfo(this._$)};break;case 30:this.$={type:"Hash",pairs:u[p],loc:l.locInfo(this._$)};break;case 31:this.$={type:"HashPair",key:l.id(u[p-2]),value:u[p],loc:l.locInfo(this._$)};break;case 32:this.$=l.id(u[p-1]);break;case 35:this.$={type:"StringLiteral",value:u[p],original:u[p],loc:l.locInfo(this._$)};break;case 36:this.$={type:"NumberLiteral",value:Number(u[p]),original:Number(u[p]),loc:l.locInfo(this._$)};break;case 37:this.$={type:"BooleanLiteral",value:u[p]==="true",original:u[p]==="true",loc:l.locInfo(this._$)};break;case 38:this.$={type:"UndefinedLiteral",original:void 0,value:void 0,loc:l.locInfo(this._$)};break;case 39:this.$={type:"NullLiteral",original:null,value:null,loc:l.locInfo(this._$)};break;case 42:this.$=l.preparePath(!0,u[p],this._$);break;case 43:this.$=l.preparePath(!1,u[p],this._$);break;case 44:u[p-2].push({part:l.id(u[p]),original:u[p],separator:u[p-1]}),this.$=u[p-2];break;case 45:this.$=[{part:l.id(u[p]),original:u[p]}];break;case 46:case 48:case 50:case 58:case 64:case 70:case 78:case 82:case 86:case 90:case 94:this.$=[];break;case 47:case 49:case 51:case 59:case 65:case 71:case 79:case 83:case 87:case 91:case 95:case 99:case 101:u[p-1].push(u[p]);break;case 98:case 100:this.$=[u[p]]}},table:[{3:1,4:2,5:[2,46],6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{1:[3]},{5:[1,4]},{5:[2,2],7:5,8:6,9:7,10:8,11:9,12:10,13:11,14:[1,12],15:[1,20],16:17,19:[1,23],24:15,27:16,29:[1,21],34:[1,22],39:[2,2],44:[2,2],47:[2,2],48:[1,13],51:[1,14],55:[1,18],59:19,60:[1,24]},{1:[2,1]},{5:[2,47],14:[2,47],15:[2,47],19:[2,47],29:[2,47],34:[2,47],39:[2,47],44:[2,47],47:[2,47],48:[2,47],51:[2,47],55:[2,47],60:[2,47]},{5:[2,3],14:[2,3],15:[2,3],19:[2,3],29:[2,3],34:[2,3],39:[2,3],44:[2,3],47:[2,3],48:[2,3],51:[2,3],55:[2,3],60:[2,3]},{5:[2,4],14:[2,4],15:[2,4],19:[2,4],29:[2,4],34:[2,4],39:[2,4],44:[2,4],47:[2,4],48:[2,4],51:[2,4],55:[2,4],60:[2,4]},{5:[2,5],14:[2,5],15:[2,5],19:[2,5],29:[2,5],34:[2,5],39:[2,5],44:[2,5],47:[2,5],48:[2,5],51:[2,5],55:[2,5],60:[2,5]},{5:[2,6],14:[2,6],15:[2,6],19:[2,6],29:[2,6],34:[2,6],39:[2,6],44:[2,6],47:[2,6],48:[2,6],51:[2,6],55:[2,6],60:[2,6]},{5:[2,7],14:[2,7],15:[2,7],19:[2,7],29:[2,7],34:[2,7],39:[2,7],44:[2,7],47:[2,7],48:[2,7],51:[2,7],55:[2,7],60:[2,7]},{5:[2,8],14:[2,8],15:[2,8],19:[2,8],29:[2,8],34:[2,8],39:[2,8],44:[2,8],47:[2,8],48:[2,8],51:[2,8],55:[2,8],60:[2,8]},{5:[2,9],14:[2,9],15:[2,9],19:[2,9],29:[2,9],34:[2,9],39:[2,9],44:[2,9],47:[2,9],48:[2,9],51:[2,9],55:[2,9],60:[2,9]},{20:25,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:36,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:37,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{4:38,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{15:[2,48],17:39,18:[2,48]},{20:41,56:40,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:44,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{5:[2,10],14:[2,10],15:[2,10],18:[2,10],19:[2,10],29:[2,10],34:[2,10],39:[2,10],44:[2,10],47:[2,10],48:[2,10],51:[2,10],55:[2,10],60:[2,10]},{20:45,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:46,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:47,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:41,56:48,64:42,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[2,78],49:49,65:[2,78],72:[2,78],80:[2,78],81:[2,78],82:[2,78],83:[2,78],84:[2,78],85:[2,78]},{23:[2,33],33:[2,33],54:[2,33],65:[2,33],68:[2,33],72:[2,33],75:[2,33],80:[2,33],81:[2,33],82:[2,33],83:[2,33],84:[2,33],85:[2,33]},{23:[2,34],33:[2,34],54:[2,34],65:[2,34],68:[2,34],72:[2,34],75:[2,34],80:[2,34],81:[2,34],82:[2,34],83:[2,34],84:[2,34],85:[2,34]},{23:[2,35],33:[2,35],54:[2,35],65:[2,35],68:[2,35],72:[2,35],75:[2,35],80:[2,35],81:[2,35],82:[2,35],83:[2,35],84:[2,35],85:[2,35]},{23:[2,36],33:[2,36],54:[2,36],65:[2,36],68:[2,36],72:[2,36],75:[2,36],80:[2,36],81:[2,36],82:[2,36],83:[2,36],84:[2,36],85:[2,36]},{23:[2,37],33:[2,37],54:[2,37],65:[2,37],68:[2,37],72:[2,37],75:[2,37],80:[2,37],81:[2,37],82:[2,37],83:[2,37],84:[2,37],85:[2,37]},{23:[2,38],33:[2,38],54:[2,38],65:[2,38],68:[2,38],72:[2,38],75:[2,38],80:[2,38],81:[2,38],82:[2,38],83:[2,38],84:[2,38],85:[2,38]},{23:[2,39],33:[2,39],54:[2,39],65:[2,39],68:[2,39],72:[2,39],75:[2,39],80:[2,39],81:[2,39],82:[2,39],83:[2,39],84:[2,39],85:[2,39]},{23:[2,43],33:[2,43],54:[2,43],65:[2,43],68:[2,43],72:[2,43],75:[2,43],80:[2,43],81:[2,43],82:[2,43],83:[2,43],84:[2,43],85:[2,43],87:[1,50]},{72:[1,35],86:51},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{52:52,54:[2,82],65:[2,82],72:[2,82],80:[2,82],81:[2,82],82:[2,82],83:[2,82],84:[2,82],85:[2,82]},{25:53,38:55,39:[1,57],43:56,44:[1,58],45:54,47:[2,54]},{28:59,43:60,44:[1,58],47:[2,56]},{13:62,15:[1,20],18:[1,61]},{33:[2,86],57:63,65:[2,86],72:[2,86],80:[2,86],81:[2,86],82:[2,86],83:[2,86],84:[2,86],85:[2,86]},{33:[2,40],65:[2,40],72:[2,40],80:[2,40],81:[2,40],82:[2,40],83:[2,40],84:[2,40],85:[2,40]},{33:[2,41],65:[2,41],72:[2,41],80:[2,41],81:[2,41],82:[2,41],83:[2,41],84:[2,41],85:[2,41]},{20:64,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:65,47:[1,66]},{30:67,33:[2,58],65:[2,58],72:[2,58],75:[2,58],80:[2,58],81:[2,58],82:[2,58],83:[2,58],84:[2,58],85:[2,58]},{33:[2,64],35:68,65:[2,64],72:[2,64],75:[2,64],80:[2,64],81:[2,64],82:[2,64],83:[2,64],84:[2,64],85:[2,64]},{21:69,23:[2,50],65:[2,50],72:[2,50],80:[2,50],81:[2,50],82:[2,50],83:[2,50],84:[2,50],85:[2,50]},{33:[2,90],61:70,65:[2,90],72:[2,90],80:[2,90],81:[2,90],82:[2,90],83:[2,90],84:[2,90],85:[2,90]},{20:74,33:[2,80],50:71,63:72,64:75,65:[1,43],69:73,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{72:[1,79]},{23:[2,42],33:[2,42],54:[2,42],65:[2,42],68:[2,42],72:[2,42],75:[2,42],80:[2,42],81:[2,42],82:[2,42],83:[2,42],84:[2,42],85:[2,42],87:[1,50]},{20:74,53:80,54:[2,84],63:81,64:75,65:[1,43],69:82,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{26:83,47:[1,66]},{47:[2,55]},{4:84,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],39:[2,46],44:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{47:[2,20]},{20:85,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{4:86,6:3,14:[2,46],15:[2,46],19:[2,46],29:[2,46],34:[2,46],47:[2,46],48:[2,46],51:[2,46],55:[2,46],60:[2,46]},{26:87,47:[1,66]},{47:[2,57]},{5:[2,11],14:[2,11],15:[2,11],19:[2,11],29:[2,11],34:[2,11],39:[2,11],44:[2,11],47:[2,11],48:[2,11],51:[2,11],55:[2,11],60:[2,11]},{15:[2,49],18:[2,49]},{20:74,33:[2,88],58:88,63:89,64:75,65:[1,43],69:90,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{65:[2,94],66:91,68:[2,94],72:[2,94],80:[2,94],81:[2,94],82:[2,94],83:[2,94],84:[2,94],85:[2,94]},{5:[2,25],14:[2,25],15:[2,25],19:[2,25],29:[2,25],34:[2,25],39:[2,25],44:[2,25],47:[2,25],48:[2,25],51:[2,25],55:[2,25],60:[2,25]},{20:92,72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,31:93,33:[2,60],63:94,64:75,65:[1,43],69:95,70:76,71:77,72:[1,78],75:[2,60],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,66],36:96,63:97,64:75,65:[1,43],69:98,70:76,71:77,72:[1,78],75:[2,66],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,22:99,23:[2,52],63:100,64:75,65:[1,43],69:101,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{20:74,33:[2,92],62:102,63:103,64:75,65:[1,43],69:104,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,105]},{33:[2,79],65:[2,79],72:[2,79],80:[2,79],81:[2,79],82:[2,79],83:[2,79],84:[2,79],85:[2,79]},{33:[2,81]},{23:[2,27],33:[2,27],54:[2,27],65:[2,27],68:[2,27],72:[2,27],75:[2,27],80:[2,27],81:[2,27],82:[2,27],83:[2,27],84:[2,27],85:[2,27]},{23:[2,28],33:[2,28],54:[2,28],65:[2,28],68:[2,28],72:[2,28],75:[2,28],80:[2,28],81:[2,28],82:[2,28],83:[2,28],84:[2,28],85:[2,28]},{23:[2,30],33:[2,30],54:[2,30],68:[2,30],71:106,72:[1,107],75:[2,30]},{23:[2,98],33:[2,98],54:[2,98],68:[2,98],72:[2,98],75:[2,98]},{23:[2,45],33:[2,45],54:[2,45],65:[2,45],68:[2,45],72:[2,45],73:[1,108],75:[2,45],80:[2,45],81:[2,45],82:[2,45],83:[2,45],84:[2,45],85:[2,45],87:[2,45]},{23:[2,44],33:[2,44],54:[2,44],65:[2,44],68:[2,44],72:[2,44],75:[2,44],80:[2,44],81:[2,44],82:[2,44],83:[2,44],84:[2,44],85:[2,44],87:[2,44]},{54:[1,109]},{54:[2,83],65:[2,83],72:[2,83],80:[2,83],81:[2,83],82:[2,83],83:[2,83],84:[2,83],85:[2,83]},{54:[2,85]},{5:[2,13],14:[2,13],15:[2,13],19:[2,13],29:[2,13],34:[2,13],39:[2,13],44:[2,13],47:[2,13],48:[2,13],51:[2,13],55:[2,13],60:[2,13]},{38:55,39:[1,57],43:56,44:[1,58],45:111,46:110,47:[2,76]},{33:[2,70],40:112,65:[2,70],72:[2,70],75:[2,70],80:[2,70],81:[2,70],82:[2,70],83:[2,70],84:[2,70],85:[2,70]},{47:[2,18]},{5:[2,14],14:[2,14],15:[2,14],19:[2,14],29:[2,14],34:[2,14],39:[2,14],44:[2,14],47:[2,14],48:[2,14],51:[2,14],55:[2,14],60:[2,14]},{33:[1,113]},{33:[2,87],65:[2,87],72:[2,87],80:[2,87],81:[2,87],82:[2,87],83:[2,87],84:[2,87],85:[2,87]},{33:[2,89]},{20:74,63:115,64:75,65:[1,43],67:114,68:[2,96],69:116,70:76,71:77,72:[1,78],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{33:[1,117]},{32:118,33:[2,62],74:119,75:[1,120]},{33:[2,59],65:[2,59],72:[2,59],75:[2,59],80:[2,59],81:[2,59],82:[2,59],83:[2,59],84:[2,59],85:[2,59]},{33:[2,61],75:[2,61]},{33:[2,68],37:121,74:122,75:[1,120]},{33:[2,65],65:[2,65],72:[2,65],75:[2,65],80:[2,65],81:[2,65],82:[2,65],83:[2,65],84:[2,65],85:[2,65]},{33:[2,67],75:[2,67]},{23:[1,123]},{23:[2,51],65:[2,51],72:[2,51],80:[2,51],81:[2,51],82:[2,51],83:[2,51],84:[2,51],85:[2,51]},{23:[2,53]},{33:[1,124]},{33:[2,91],65:[2,91],72:[2,91],80:[2,91],81:[2,91],82:[2,91],83:[2,91],84:[2,91],85:[2,91]},{33:[2,93]},{5:[2,22],14:[2,22],15:[2,22],19:[2,22],29:[2,22],34:[2,22],39:[2,22],44:[2,22],47:[2,22],48:[2,22],51:[2,22],55:[2,22],60:[2,22]},{23:[2,99],33:[2,99],54:[2,99],68:[2,99],72:[2,99],75:[2,99]},{73:[1,108]},{20:74,63:125,64:75,65:[1,43],72:[1,35],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,23],14:[2,23],15:[2,23],19:[2,23],29:[2,23],34:[2,23],39:[2,23],44:[2,23],47:[2,23],48:[2,23],51:[2,23],55:[2,23],60:[2,23]},{47:[2,19]},{47:[2,77]},{20:74,33:[2,72],41:126,63:127,64:75,65:[1,43],69:128,70:76,71:77,72:[1,78],75:[2,72],78:26,79:27,80:[1,28],81:[1,29],82:[1,30],83:[1,31],84:[1,32],85:[1,34],86:33},{5:[2,24],14:[2,24],15:[2,24],19:[2,24],29:[2,24],34:[2,24],39:[2,24],44:[2,24],47:[2,24],48:[2,24],51:[2,24],55:[2,24],60:[2,24]},{68:[1,129]},{65:[2,95],68:[2,95],72:[2,95],80:[2,95],81:[2,95],82:[2,95],83:[2,95],84:[2,95],85:[2,95]},{68:[2,97]},{5:[2,21],14:[2,21],15:[2,21],19:[2,21],29:[2,21],34:[2,21],39:[2,21],44:[2,21],47:[2,21],48:[2,21],51:[2,21],55:[2,21],60:[2,21]},{33:[1,130]},{33:[2,63]},{72:[1,132],76:131},{33:[1,133]},{33:[2,69]},{15:[2,12],18:[2,12]},{14:[2,26],15:[2,26],19:[2,26],29:[2,26],34:[2,26],47:[2,26],48:[2,26],51:[2,26],55:[2,26],60:[2,26]},{23:[2,31],33:[2,31],54:[2,31],68:[2,31],72:[2,31],75:[2,31]},{33:[2,74],42:134,74:135,75:[1,120]},{33:[2,71],65:[2,71],72:[2,71],75:[2,71],80:[2,71],81:[2,71],82:[2,71],83:[2,71],84:[2,71],85:[2,71]},{33:[2,73],75:[2,73]},{23:[2,29],33:[2,29],54:[2,29],65:[2,29],68:[2,29],72:[2,29],75:[2,29],80:[2,29],81:[2,29],82:[2,29],83:[2,29],84:[2,29],85:[2,29]},{14:[2,15],15:[2,15],19:[2,15],29:[2,15],34:[2,15],39:[2,15],44:[2,15],47:[2,15],48:[2,15],51:[2,15],55:[2,15],60:[2,15]},{72:[1,137],77:[1,136]},{72:[2,100],77:[2,100]},{14:[2,16],15:[2,16],19:[2,16],29:[2,16],34:[2,16],44:[2,16],47:[2,16],48:[2,16],51:[2,16],55:[2,16],60:[2,16]},{33:[1,138]},{33:[2,75]},{33:[2,32]},{72:[2,101],77:[2,101]},{14:[2,17],15:[2,17],19:[2,17],29:[2,17],34:[2,17],39:[2,17],44:[2,17],47:[2,17],48:[2,17],51:[2,17],55:[2,17],60:[2,17]}],defaultActions:{4:[2,1],54:[2,55],56:[2,20],60:[2,57],73:[2,81],82:[2,85],86:[2,18],90:[2,89],101:[2,53],104:[2,93],110:[2,19],111:[2,77],116:[2,97],119:[2,63],122:[2,69],135:[2,75],136:[2,32]},parseError:function(o,i){throw new Error(o)},parse:function(o){var i=this,c=[0],l=[null],d=[],u=this.table,h="",p=0,m=0;this.lexer.setInput(o),this.lexer.yy=this.yy,this.yy.lexer=this.lexer,this.yy.parser=this,this.lexer.yylloc===void 0&&(this.lexer.yylloc={});var y=this.lexer.yylloc;d.push(y);var f=this.lexer.options&&this.lexer.options.ranges;typeof this.yy.parseError=="function"&&(this.parseError=this.yy.parseError);for(var b,S,v,T,w,I,k,U,D,N={};;){if(S=c[c.length-1],this.defaultActions[S]?v=this.defaultActions[S]:(b==null&&(D=void 0,typeof(D=i.lexer.lex()||1)!="number"&&(D=i.symbols_[D]||D),b=D),v=u[S]&&u[S][b]),v===void 0||!v.length||!v[0]){var X="";for(w in U=[],u[S])this.terminals_[w]&&w>2&&U.push("'"+this.terminals_[w]+"'");X=this.lexer.showPosition?"Parse error on line "+(p+1)+`:
`+this.lexer.showPosition()+`
Expecting `+U.join(", ")+", got '"+(this.terminals_[b]||b)+"'":"Parse error on line "+(p+1)+": Unexpected "+(b==1?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(X,{text:this.lexer.match,token:this.terminals_[b]||b,line:this.lexer.yylineno,loc:y,expected:U})}if(v[0]instanceof Array&&v.length>1)throw new Error("Parse Error: multiple actions possible at state: "+S+", token: "+b);switch(v[0]){case 1:c.push(b),l.push(this.lexer.yytext),d.push(this.lexer.yylloc),c.push(v[1]),b=null,m=this.lexer.yyleng,h=this.lexer.yytext,p=this.lexer.yylineno,y=this.lexer.yylloc;break;case 2:if(I=this.productions_[v[1]][1],N.$=l[l.length-I],N._$={first_line:d[d.length-(I||1)].first_line,last_line:d[d.length-1].last_line,first_column:d[d.length-(I||1)].first_column,last_column:d[d.length-1].last_column},f&&(N._$.range=[d[d.length-(I||1)].range[0],d[d.length-1].range[1]]),(T=this.performAction.call(N,h,m,p,this.yy,v[1],l,d))!==void 0)return T;I&&(c=c.slice(0,-1*I*2),l=l.slice(0,-1*I),d=d.slice(0,-1*I)),c.push(this.productions_[v[1]][0]),l.push(N.$),d.push(N._$),k=u[c[c.length-2]][c[c.length-1]],c.push(k);break;case 3:return!0}}return!0}},r=function(){var o={EOF:1,parseError:function(i,c){if(!this.yy.parser)throw new Error(i);this.yy.parser.parseError(i,c)},setInput:function(i){return this._input=i,this._more=this._less=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var i=this._input[0];return this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i,i.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},unput:function(i){var c=i.length,l=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-c-1),this.offset-=c;var d=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),l.length-1&&(this.yylineno-=l.length-1);var u=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:l?(l.length===d.length?this.yylloc.first_column:0)+d[d.length-l.length].length-l[0].length:this.yylloc.first_column-c},this.options.ranges&&(this.yylloc.range=[u[0],u[0]+this.yyleng-c]),this},more:function(){return this._more=!0,this},less:function(i){this.unput(this.match.slice(i))},pastInput:function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var i=this.pastInput(),c=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+c+"^"},next:function(){if(this.done)return this.EOF;var i,c,l,d,u;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var h=this._currentRules(),p=0;p<h.length&&(!(l=this._input.match(this.rules[h[p]]))||c&&!(l[0].length>c[0].length)||(c=l,d=p,this.options.flex));p++);return c?((u=c[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+c[0].length},this.yytext+=c[0],this.match+=c[0],this.matches=c,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._input=this._input.slice(c[0].length),this.matched+=c[0],i=this.performAction.call(this,this.yy,this,h[d],this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),i||void 0):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var i=this.next();return i!==void 0?i:this.lex()},begin:function(i){this.conditionStack.push(i)},popState:function(){return this.conditionStack.pop()},_currentRules:function(){return this.conditions[this.conditionStack[this.conditionStack.length-1]].rules},topState:function(){return this.conditionStack[this.conditionStack.length-2]},pushState:function(i){this.begin(i)},options:{},performAction:function(i,c,l,d){function u(h,p){return c.yytext=c.yytext.substring(h,c.yyleng-p+h)}switch(l){case 0:if(c.yytext.slice(-2)==="\\\\"?(u(0,1),this.begin("mu")):c.yytext.slice(-1)==="\\"?(u(0,1),this.begin("emu")):this.begin("mu"),c.yytext)return 15;break;case 1:case 5:return 15;case 2:return this.popState(),15;case 3:return this.begin("raw"),15;case 4:return this.popState(),this.conditionStack[this.conditionStack.length-1]==="raw"?15:(u(5,9),"END_RAW_BLOCK");case 6:case 22:return this.popState(),14;case 7:return 65;case 8:return 68;case 9:return 19;case 10:return this.popState(),this.begin("raw"),23;case 11:return 55;case 12:return 60;case 13:return 29;case 14:return 47;case 15:case 16:return this.popState(),44;case 17:return 34;case 18:return 39;case 19:return 51;case 20:case 23:return 48;case 21:this.unput(c.yytext),this.popState(),this.begin("com");break;case 24:return 73;case 25:case 26:case 41:return 72;case 27:return 87;case 28:break;case 29:return this.popState(),54;case 30:return this.popState(),33;case 31:return c.yytext=u(1,2).replace(/\\"/g,'"'),80;case 32:return c.yytext=u(1,2).replace(/\\'/g,"'"),80;case 33:return 85;case 34:case 35:return 82;case 36:return 83;case 37:return 84;case 38:return 81;case 39:return 75;case 40:return 77;case 42:return c.yytext=c.yytext.replace(/\\([\\\]])/g,"$1"),72;case 43:return"INVALID";case 44:return 5}},rules:[/^(?:[^\x00]*?(?=(\{\{)))/,/^(?:[^\x00]+)/,/^(?:[^\x00]{2,}?(?=(\{\{|\\\{\{|\\\\\{\{|$)))/,/^(?:\{\{\{\{(?=[^/]))/,/^(?:\{\{\{\{\/[^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=[=}\s\/.])\}\}\}\})/,/^(?:[^\x00]+?(?=(\{\{\{\{)))/,/^(?:[\s\S]*?--(~)?\}\})/,/^(?:\()/,/^(?:\))/,/^(?:\{\{\{\{)/,/^(?:\}\}\}\})/,/^(?:\{\{(~)?>)/,/^(?:\{\{(~)?#>)/,/^(?:\{\{(~)?#\*?)/,/^(?:\{\{(~)?\/)/,/^(?:\{\{(~)?\^\s*(~)?\}\})/,/^(?:\{\{(~)?\s*else\s*(~)?\}\})/,/^(?:\{\{(~)?\^)/,/^(?:\{\{(~)?\s*else\b)/,/^(?:\{\{(~)?\{)/,/^(?:\{\{(~)?&)/,/^(?:\{\{(~)?!--)/,/^(?:\{\{(~)?![\s\S]*?\}\})/,/^(?:\{\{(~)?\*?)/,/^(?:=)/,/^(?:\.\.)/,/^(?:\.(?=([=~}\s\/.)|])))/,/^(?:[\/.])/,/^(?:\s+)/,/^(?:\}(~)?\}\})/,/^(?:(~)?\}\})/,/^(?:"(\\["]|[^"])*")/,/^(?:'(\\[']|[^'])*')/,/^(?:@)/,/^(?:true(?=([~}\s)])))/,/^(?:false(?=([~}\s)])))/,/^(?:undefined(?=([~}\s)])))/,/^(?:null(?=([~}\s)])))/,/^(?:-?[0-9]+(?:\.[0-9]+)?(?=([~}\s)])))/,/^(?:as\s+\|)/,/^(?:\|)/,/^(?:([^\s!"#%-,\.\/;->@\[-\^`\{-~]+(?=([=~}\s\/.)|]))))/,/^(?:\[(\\\]|[^\]])*\])/,/^(?:.)/,/^(?:$)/],conditions:{mu:{rules:[7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44],inclusive:!1},emu:{rules:[2],inclusive:!1},com:{rules:[6],inclusive:!1},raw:{rules:[3,4,5],inclusive:!1},INITIAL:{rules:[0,1,44],inclusive:!0}}};return o}();function a(){this.yy={}}return s.lexer=r,a.prototype=s,s.Parser=a,new a}();t.default=n,e.exports=t.default})(lr,lr.exports);var Yd=lr.exports,ur={exports:{}},cr={exports:{}};(function(e,t){t.__esModule=!0;var n=function(i){return i&&i.__esModule?i:{default:i}}(Te);function s(){this.parents=[]}function r(i){this.acceptRequired(i,"path"),this.acceptArray(i.params),this.acceptKey(i,"hash")}function a(i){r.call(this,i),this.acceptKey(i,"program"),this.acceptKey(i,"inverse")}function o(i){this.acceptRequired(i,"name"),this.acceptArray(i.params),this.acceptKey(i,"hash")}s.prototype={constructor:s,mutating:!1,acceptKey:function(i,c){var l=this.accept(i[c]);if(this.mutating){if(l&&!s.prototype[l.type])throw new n.default('Unexpected node type "'+l.type+'" found when accepting '+c+" on "+i.type);i[c]=l}},acceptRequired:function(i,c){if(this.acceptKey(i,c),!i[c])throw new n.default(i.type+" requires "+c)},acceptArray:function(i){for(var c=0,l=i.length;c<l;c++)this.acceptKey(i,c),i[c]||(i.splice(c,1),c--,l--)},accept:function(i){if(i){if(!this[i.type])throw new n.default("Unknown type: "+i.type,i);this.current&&this.parents.unshift(this.current),this.current=i;var c=this[i.type](i);return this.current=this.parents.shift(),!this.mutating||c?c:c!==!1?i:void 0}},Program:function(i){this.acceptArray(i.body)},MustacheStatement:r,Decorator:r,BlockStatement:a,DecoratorBlock:a,PartialStatement:o,PartialBlockStatement:function(i){o.call(this,i),this.acceptKey(i,"program")},ContentStatement:function(){},CommentStatement:function(){},SubExpression:r,PathExpression:function(){},StringLiteral:function(){},NumberLiteral:function(){},BooleanLiteral:function(){},UndefinedLiteral:function(){},NullLiteral:function(){},Hash:function(i){this.acceptArray(i.pairs)},HashPair:function(i){this.acceptRequired(i,"value")}},t.default=s,e.exports=t.default})(cr,cr.exports);var tl=cr.exports;(function(e,t){t.__esModule=!0;var n=function(c){return c&&c.__esModule?c:{default:c}}(tl);function s(){var c=arguments.length<=0||arguments[0]===void 0?{}:arguments[0];this.options=c}function r(c,l,d){l===void 0&&(l=c.length);var u=c[l-1],h=c[l-2];return u?u.type==="ContentStatement"?(h||!d?/\r?\n\s*?$/:/(^|\r?\n)\s*?$/).test(u.original):void 0:d}function a(c,l,d){l===void 0&&(l=-1);var u=c[l+1],h=c[l+2];return u?u.type==="ContentStatement"?(h||!d?/^\s*?\r?\n/:/^\s*?(\r?\n|$)/).test(u.original):void 0:d}function o(c,l,d){var u=c[l==null?0:l+1];if(u&&u.type==="ContentStatement"&&(d||!u.rightStripped)){var h=u.value;u.value=u.value.replace(d?/^\s+/:/^[ \t]*\r?\n?/,""),u.rightStripped=u.value!==h}}function i(c,l,d){var u=c[l==null?c.length-1:l-1];if(u&&u.type==="ContentStatement"&&(d||!u.leftStripped)){var h=u.value;return u.value=u.value.replace(d?/\s+$/:/[ \t]+$/,""),u.leftStripped=u.value!==h,u.leftStripped}}s.prototype=new n.default,s.prototype.Program=function(c){var l=!this.options.ignoreStandalone,d=!this.isRootSeen;this.isRootSeen=!0;for(var u=c.body,h=0,p=u.length;h<p;h++){var m=u[h],y=this.accept(m);if(y){var f=r(u,h,d),b=a(u,h,d),S=y.openStandalone&&f,v=y.closeStandalone&&b,T=y.inlineStandalone&&f&&b;y.close&&o(u,h,!0),y.open&&i(u,h,!0),l&&T&&(o(u,h),i(u,h)&&m.type==="PartialStatement"&&(m.indent=/([ \t]+$)/.exec(u[h-1].original)[1])),l&&S&&(o((m.program||m.inverse).body),i(u,h)),l&&v&&(o(u,h),i((m.inverse||m.program).body))}}return c},s.prototype.BlockStatement=s.prototype.DecoratorBlock=s.prototype.PartialBlockStatement=function(c){this.accept(c.program),this.accept(c.inverse);var l=c.program||c.inverse,d=c.program&&c.inverse,u=d,h=d;if(d&&d.chained)for(u=d.body[0].program;h.chained;)h=h.body[h.body.length-1].program;var p={open:c.openStrip.open,close:c.closeStrip.close,openStandalone:a(l.body),closeStandalone:r((u||l).body)};if(c.openStrip.close&&o(l.body,null,!0),d){var m=c.inverseStrip;m.open&&i(l.body,null,!0),m.close&&o(u.body,null,!0),c.closeStrip.open&&i(h.body,null,!0),!this.options.ignoreStandalone&&r(l.body)&&a(u.body)&&(i(l.body),o(u.body))}else c.closeStrip.open&&i(l.body,null,!0);return p},s.prototype.Decorator=s.prototype.MustacheStatement=function(c){return c.strip},s.prototype.PartialStatement=s.prototype.CommentStatement=function(c){var l=c.strip||{};return{inlineStandalone:!0,open:l.open,close:l.close}},t.default=s,e.exports=t.default})(ur,ur.exports);var Kd=ur.exports,pe={};pe.__esModule=!0,pe.SourceLocation=function(e,t){this.source=e,this.start={line:t.first_line,column:t.first_column},this.end={line:t.last_line,column:t.last_column}},pe.id=function(e){return/^\[.*\]$/.test(e)?e.substring(1,e.length-1):e},pe.stripFlags=function(e,t){return{open:e.charAt(2)==="~",close:t.charAt(t.length-3)==="~"}},pe.stripComment=function(e){return e.replace(/^\{\{~?!-?-?/,"").replace(/-?-?~?\}\}$/,"")},pe.preparePath=function(e,t,n){n=this.locInfo(n);for(var s=e?"@":"",r=[],a=0,o=0,i=t.length;o<i;o++){var c=t[o].part,l=t[o].original!==c;if(s+=(t[o].separator||"")+c,l||c!==".."&&c!=="."&&c!=="this")r.push(c);else{if(r.length>0)throw new dr.default("Invalid path: "+s,{loc:n});c===".."&&a++}}return{type:"PathExpression",data:e,depth:a,parts:r,original:s,loc:n}},pe.prepareMustache=function(e,t,n,s,r,a){var o=s.charAt(3)||s.charAt(2),i=o!=="{"&&o!=="&";return{type:/\*/.test(s)?"Decorator":"MustacheStatement",path:e,params:t,hash:n,escaped:i,strip:r,loc:this.locInfo(a)}},pe.prepareRawBlock=function(e,t,n,s){vs(e,n),s=this.locInfo(s);var r={type:"Program",body:t,strip:{},loc:s};return{type:"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:r,openStrip:{},inverseStrip:{},closeStrip:{},loc:s}},pe.prepareBlock=function(e,t,n,s,r,a){s&&s.path&&vs(e,s);var o=/\*/.test(e.open);t.blockParams=e.blockParams;var i=void 0,c=void 0;if(n){if(o)throw new dr.default("Unexpected inverse block on decorator",n);n.chain&&(n.program.body[0].closeStrip=s.strip),c=n.strip,i=n.program}return r&&(r=i,i=t,t=r),{type:o?"DecoratorBlock":"BlockStatement",path:e.path,params:e.params,hash:e.hash,program:t,inverse:i,openStrip:e.strip,inverseStrip:c,closeStrip:s&&s.strip,loc:this.locInfo(a)}},pe.prepareProgram=function(e,t){if(!t&&e.length){var n=e[0].loc,s=e[e.length-1].loc;n&&s&&(t={source:n.source,start:{line:n.start.line,column:n.start.column},end:{line:s.end.line,column:s.end.column}})}return{type:"Program",body:e,strip:{},loc:t}},pe.preparePartialBlock=function(e,t,n,s){return vs(e,n),{type:"PartialBlockStatement",name:e.path,params:e.params,hash:e.hash,program:t,openStrip:e.strip,closeStrip:n&&n.strip,loc:this.locInfo(s)}};var dr=function(e){return e&&e.__esModule?e:{default:e}}(Te);function vs(e,t){if(t=t.path?t.path.original:t,e.path.original!==t){var n={loc:e.path.loc};throw new dr.default(e.path.original+" doesn't match "+t,n)}}function nl(e){return e&&e.__esModule?e:{default:e}}Ft.__esModule=!0,Ft.parseWithoutProcessing=io,Ft.parse=function(e,t){var n=io(e,t);return new Wd.default(t).accept(n)};var hr=nl(Yd),Wd=nl(Kd),zd=function(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(pe),Xd=P;Ft.parser=hr.default;var Sn={};function io(e,t){return e.type==="Program"?e:(hr.default.yy=Sn,Sn.locInfo=function(n){return new Sn.SourceLocation(t&&t.srcName,n)},hr.default.parse(e))}Xd.extend(Sn,zd);var Rt={};function sl(e){return e&&e.__esModule?e:{default:e}}Rt.__esModule=!0,Rt.Compiler=pr,Rt.precompile=function(e,t,n){if(e==null||typeof e!="string"&&e.type!=="Program")throw new Ut.default("You must pass a string or Handlebars AST to Handlebars.precompile. You passed "+e);"data"in(t=t||{})||(t.data=!0),t.compat&&(t.useDepths=!0);var s=n.parse(e,t),r=new n.Compiler().compile(s,t);return new n.JavaScriptCompiler().compile(r,t)},Rt.compile=function(e,t,n){if(t===void 0&&(t={}),e==null||typeof e!="string"&&e.type!=="Program")throw new Ut.default("You must pass a string or Handlebars AST to Handlebars.compile. You passed "+e);"data"in(t=Yt.extend({},t))||(t.data=!0),t.compat&&(t.useDepths=!0);var s=void 0;function r(){var o=n.parse(e,t),i=new n.Compiler().compile(o,t),c=new n.JavaScriptCompiler().compile(i,t,void 0,!0);return n.template(c)}function a(o,i){return s||(s=r()),s.call(this,o,i)}return a._setup=function(o){return s||(s=r()),s._setup(o)},a._child=function(o,i,c,l){return s||(s=r()),s._child(o,i,c,l)},a};var Ut=sl(Te),Yt=P,wt=sl(el),Jd=[].slice;function pr(){}function rl(e,t){if(e===t)return!0;if(Yt.isArray(e)&&Yt.isArray(t)&&e.length===t.length){for(var n=0;n<e.length;n++)if(!rl(e[n],t[n]))return!1;return!0}}function lo(e){if(!e.path.parts){var t=e.path;e.path={type:"PathExpression",data:!1,depth:0,parts:[t.original+""],original:t.original+"",loc:t.loc}}}pr.prototype={compiler:pr,equals:function(e){var t=this.opcodes.length;if(e.opcodes.length!==t)return!1;for(var n=0;n<t;n++){var s=this.opcodes[n],r=e.opcodes[n];if(s.opcode!==r.opcode||!rl(s.args,r.args))return!1}for(t=this.children.length,n=0;n<t;n++)if(!this.children[n].equals(e.children[n]))return!1;return!0},guid:0,compile:function(e,t){return this.sourceNode=[],this.opcodes=[],this.children=[],this.options=t,this.stringParams=t.stringParams,this.trackIds=t.trackIds,t.blockParams=t.blockParams||[],t.knownHelpers=Yt.extend(Object.create(null),{helperMissing:!0,blockHelperMissing:!0,each:!0,if:!0,unless:!0,with:!0,log:!0,lookup:!0},t.knownHelpers),this.accept(e)},compileProgram:function(e){var t=new this.compiler().compile(e,this.options),n=this.guid++;return this.usePartial=this.usePartial||t.usePartial,this.children[n]=t,this.useDepths=this.useDepths||t.useDepths,n},accept:function(e){if(!this[e.type])throw new Ut.default("Unknown type: "+e.type,e);this.sourceNode.unshift(e);var t=this[e.type](e);return this.sourceNode.shift(),t},Program:function(e){this.options.blockParams.unshift(e.blockParams);for(var t=e.body,n=t.length,s=0;s<n;s++)this.accept(t[s]);return this.options.blockParams.shift(),this.isSimple=n===1,this.blockParams=e.blockParams?e.blockParams.length:0,this},BlockStatement:function(e){lo(e);var t=e.program,n=e.inverse;t=t&&this.compileProgram(t),n=n&&this.compileProgram(n);var s=this.classifySexpr(e);s==="helper"?this.helperSexpr(e,t,n):s==="simple"?(this.simpleSexpr(e),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("blockValue",e.path.original)):(this.ambiguousSexpr(e,t,n),this.opcode("pushProgram",t),this.opcode("pushProgram",n),this.opcode("emptyHash"),this.opcode("ambiguousBlockValue")),this.opcode("append")},DecoratorBlock:function(e){var t=e.program&&this.compileProgram(e.program),n=this.setupFullMustacheParams(e,t,void 0),s=e.path;this.useDecorators=!0,this.opcode("registerDecorator",n.length,s.original)},PartialStatement:function(e){this.usePartial=!0;var t=e.program;t&&(t=this.compileProgram(e.program));var n=e.params;if(n.length>1)throw new Ut.default("Unsupported number of partial arguments: "+n.length,e);n.length||(this.options.explicitPartialContext?this.opcode("pushLiteral","undefined"):n.push({type:"PathExpression",parts:[],depth:0}));var s=e.name.original,r=e.name.type==="SubExpression";r&&this.accept(e.name),this.setupFullMustacheParams(e,t,void 0,!0);var a=e.indent||"";this.options.preventIndent&&a&&(this.opcode("appendContent",a),a=""),this.opcode("invokePartial",r,s,a),this.opcode("append")},PartialBlockStatement:function(e){this.PartialStatement(e)},MustacheStatement:function(e){this.SubExpression(e),e.escaped&&!this.options.noEscape?this.opcode("appendEscaped"):this.opcode("append")},Decorator:function(e){this.DecoratorBlock(e)},ContentStatement:function(e){e.value&&this.opcode("appendContent",e.value)},CommentStatement:function(){},SubExpression:function(e){lo(e);var t=this.classifySexpr(e);t==="simple"?this.simpleSexpr(e):t==="helper"?this.helperSexpr(e):this.ambiguousSexpr(e)},ambiguousSexpr:function(e,t,n){var s=e.path,r=s.parts[0],a=t!=null||n!=null;this.opcode("getContext",s.depth),this.opcode("pushProgram",t),this.opcode("pushProgram",n),s.strict=!0,this.accept(s),this.opcode("invokeAmbiguous",r,a)},simpleSexpr:function(e){var t=e.path;t.strict=!0,this.accept(t),this.opcode("resolvePossibleLambda")},helperSexpr:function(e,t,n){var s=this.setupFullMustacheParams(e,t,n),r=e.path,a=r.parts[0];if(this.options.knownHelpers[a])this.opcode("invokeKnownHelper",s.length,a);else{if(this.options.knownHelpersOnly)throw new Ut.default("You specified knownHelpersOnly, but used the unknown helper "+a,e);r.strict=!0,r.falsy=!0,this.accept(r),this.opcode("invokeHelper",s.length,r.original,wt.default.helpers.simpleId(r))}},PathExpression:function(e){this.addDepth(e.depth),this.opcode("getContext",e.depth);var t=e.parts[0],n=wt.default.helpers.scopedId(e),s=!e.depth&&!n&&this.blockParamIndex(t);s?this.opcode("lookupBlockParam",s,e.parts):t?e.data?(this.options.data=!0,this.opcode("lookupData",e.depth,e.parts,e.strict)):this.opcode("lookupOnContext",e.parts,e.falsy,e.strict,n):this.opcode("pushContext")},StringLiteral:function(e){this.opcode("pushString",e.value)},NumberLiteral:function(e){this.opcode("pushLiteral",e.value)},BooleanLiteral:function(e){this.opcode("pushLiteral",e.value)},UndefinedLiteral:function(){this.opcode("pushLiteral","undefined")},NullLiteral:function(){this.opcode("pushLiteral","null")},Hash:function(e){var t=e.pairs,n=0,s=t.length;for(this.opcode("pushHash");n<s;n++)this.pushParam(t[n].value);for(;n--;)this.opcode("assignToHash",t[n].key);this.opcode("popHash")},opcode:function(e){this.opcodes.push({opcode:e,args:Jd.call(arguments,1),loc:this.sourceNode[0].loc})},addDepth:function(e){e&&(this.useDepths=!0)},classifySexpr:function(e){var t=wt.default.helpers.simpleId(e.path),n=t&&!!this.blockParamIndex(e.path.parts[0]),s=!n&&wt.default.helpers.helperExpression(e),r=!n&&(s||t);if(r&&!s){var a=e.path.parts[0],o=this.options;o.knownHelpers[a]?s=!0:o.knownHelpersOnly&&(r=!1)}return s?"helper":r?"ambiguous":"simple"},pushParams:function(e){for(var t=0,n=e.length;t<n;t++)this.pushParam(e[t])},pushParam:function(e){var t=e.value!=null?e.value:e.original||"";if(this.stringParams)t.replace&&(t=t.replace(/^(\.?\.\/)*/g,"").replace(/\//g,".")),e.depth&&this.addDepth(e.depth),this.opcode("getContext",e.depth||0),this.opcode("pushStringParam",t,e.type),e.type==="SubExpression"&&this.accept(e);else{if(this.trackIds){var n=void 0;if(!e.parts||wt.default.helpers.scopedId(e)||e.depth||(n=this.blockParamIndex(e.parts[0])),n){var s=e.parts.slice(1).join(".");this.opcode("pushId","BlockParam",n,s)}else(t=e.original||t).replace&&(t=t.replace(/^this(?:\.|$)/,"").replace(/^\.\//,"").replace(/^\.$/,"")),this.opcode("pushId",e.type,t)}this.accept(e)}},setupFullMustacheParams:function(e,t,n,s){var r=e.params;return this.pushParams(r),this.opcode("pushProgram",t),this.opcode("pushProgram",n),e.hash?this.accept(e.hash):this.opcode("emptyHash",s),r},blockParamIndex:function(e){for(var t=0,n=this.options.blockParams.length;t<n;t++){var s=this.options.blockParams[t],r=s&&Yt.indexOf(s,e);if(s&&r>=0)return[t,r]}}};var uo,co,mr={exports:{}},gr={exports:{}},on={},Ss={},ln={},un={};function Zd(){if(uo)return un;uo=1;var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");return un.encode=function(t){if(0<=t&&t<e.length)return e[t];throw new TypeError("Must be between 0 and 63: "+t)},un.decode=function(t){return 65<=t&&t<=90?t-65:97<=t&&t<=122?t-97+26:48<=t&&t<=57?t-48+52:t==43?62:t==47?63:-1},un}function al(){if(co)return ln;co=1;var e=Zd();return ln.encode=function(t){var n,s="",r=function(a){return a<0?1+(-a<<1):0+(a<<1)}(t);do n=31&r,(r>>>=5)>0&&(n|=32),s+=e.encode(n);while(r>0);return s},ln.decode=function(t,n,s){var r,a,o,i,c=t.length,l=0,d=0;do{if(n>=c)throw new Error("Expected more digits in base 64 VLQ value.");if((a=e.decode(t.charCodeAt(n++)))===-1)throw new Error("Invalid base64 digit: "+t.charAt(n-1));r=!!(32&a),l+=(a&=31)<<d,d+=5}while(r);s.value=(i=(o=l)>>1,1&~o?i:-i),s.rest=n},ln}var ho,po={};function Kt(){return ho||(ho=1,function(e){e.getArg=function(u,h,p){if(h in u)return u[h];if(arguments.length===3)return p;throw new Error('"'+h+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,n=/^data:.+\,.+$/;function s(u){var h=u.match(t);return h?{scheme:h[1],auth:h[2],host:h[3],port:h[4],path:h[5]}:null}function r(u){var h="";return u.scheme&&(h+=u.scheme+":"),h+="//",u.auth&&(h+=u.auth+"@"),u.host&&(h+=u.host),u.port&&(h+=":"+u.port),u.path&&(h+=u.path),h}function a(u){var h=u,p=s(u);if(p){if(!p.path)return u;h=p.path}for(var m,y=e.isAbsolute(h),f=h.split(/\/+/),b=0,S=f.length-1;S>=0;S--)(m=f[S])==="."?f.splice(S,1):m===".."?b++:b>0&&(m===""?(f.splice(S+1,b),b=0):(f.splice(S,2),b--));return(h=f.join("/"))===""&&(h=y?"/":"."),p?(p.path=h,r(p)):h}function o(u,h){u===""&&(u="."),h===""&&(h=".");var p=s(h),m=s(u);if(m&&(u=m.path||"/"),p&&!p.scheme)return m&&(p.scheme=m.scheme),r(p);if(p||h.match(n))return h;if(m&&!m.host&&!m.path)return m.host=h,r(m);var y=h.charAt(0)==="/"?h:a(u.replace(/\/+$/,"")+"/"+h);return m?(m.path=y,r(m)):y}e.urlParse=s,e.urlGenerate=r,e.normalize=a,e.join=o,e.isAbsolute=function(u){return u.charAt(0)==="/"||t.test(u)},e.relative=function(u,h){u===""&&(u="."),u=u.replace(/\/$/,"");for(var p=0;h.indexOf(u+"/")!==0;){var m=u.lastIndexOf("/");if(m<0||(u=u.slice(0,m)).match(/^([^\/]+:\/)?\/*$/))return h;++p}return Array(p+1).join("../")+h.substr(u.length+1)};var i=!("__proto__"in Object.create(null));function c(u){return u}function l(u){if(!u)return!1;var h=u.length;if(h<9||u.charCodeAt(h-1)!==95||u.charCodeAt(h-2)!==95||u.charCodeAt(h-3)!==111||u.charCodeAt(h-4)!==116||u.charCodeAt(h-5)!==111||u.charCodeAt(h-6)!==114||u.charCodeAt(h-7)!==112||u.charCodeAt(h-8)!==95||u.charCodeAt(h-9)!==95)return!1;for(var p=h-10;p>=0;p--)if(u.charCodeAt(p)!==36)return!1;return!0}function d(u,h){return u===h?0:u===null?1:h===null?-1:u>h?1:-1}e.toSetString=i?c:function(u){return l(u)?"$"+u:u},e.fromSetString=i?c:function(u){return l(u)?u.slice(1):u},e.compareByOriginalPositions=function(u,h,p){var m=d(u.source,h.source);return m!==0||(m=u.originalLine-h.originalLine)!==0||(m=u.originalColumn-h.originalColumn)!==0||p||(m=u.generatedColumn-h.generatedColumn)!==0||(m=u.generatedLine-h.generatedLine)!==0?m:d(u.name,h.name)},e.compareByGeneratedPositionsDeflated=function(u,h,p){var m=u.generatedLine-h.generatedLine;return m!==0||(m=u.generatedColumn-h.generatedColumn)!==0||p||(m=d(u.source,h.source))!==0||(m=u.originalLine-h.originalLine)!==0||(m=u.originalColumn-h.originalColumn)!==0?m:d(u.name,h.name)},e.compareByGeneratedPositionsInflated=function(u,h){var p=u.generatedLine-h.generatedLine;return p!==0||(p=u.generatedColumn-h.generatedColumn)!==0||(p=d(u.source,h.source))!==0||(p=u.originalLine-h.originalLine)!==0||(p=u.originalColumn-h.originalColumn)!==0?p:d(u.name,h.name)},e.parseSourceMapInput=function(u){return JSON.parse(u.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(u,h,p){if(h=h||"",u&&(u[u.length-1]!=="/"&&h[0]!=="/"&&(u+="/"),h=u+h),p){var m=s(p);if(!m)throw new Error("sourceMapURL could not be parsed");if(m.path){var y=m.path.lastIndexOf("/");y>=0&&(m.path=m.path.substring(0,y+1))}h=o(r(m),h)}return a(h)}}(po)),po}var mo,Es={};function ol(){if(mo)return Es;mo=1;var e=Kt(),t=Object.prototype.hasOwnProperty,n=typeof Map<"u";function s(){this._array=[],this._set=n?new Map:Object.create(null)}return s.fromArray=function(r,a){for(var o=new s,i=0,c=r.length;i<c;i++)o.add(r[i],a);return o},s.prototype.size=function(){return n?this._set.size:Object.getOwnPropertyNames(this._set).length},s.prototype.add=function(r,a){var o=n?r:e.toSetString(r),i=n?this.has(r):t.call(this._set,o),c=this._array.length;i&&!a||this._array.push(r),i||(n?this._set.set(r,c):this._set[o]=c)},s.prototype.has=function(r){if(n)return this._set.has(r);var a=e.toSetString(r);return t.call(this._set,a)},s.prototype.indexOf=function(r){if(n){var a=this._set.get(r);if(a>=0)return a}else{var o=e.toSetString(r);if(t.call(this._set,o))return this._set[o]}throw new Error('"'+r+'" is not in the set.')},s.prototype.at=function(r){if(r>=0&&r<this._array.length)return this._array[r];throw new Error("No element indexed by "+r)},s.prototype.toArray=function(){return this._array.slice()},Es.ArraySet=s,Es}var go,fo,Ts={};function Qd(){if(go)return Ts;go=1;var e=Kt();function t(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}return t.prototype.unsortedForEach=function(n,s){this._array.forEach(n,s)},t.prototype.add=function(n){var s,r,a,o,i,c;s=this._last,r=n,a=s.generatedLine,o=r.generatedLine,i=s.generatedColumn,c=r.generatedColumn,o>a||o==a&&c>=i||e.compareByGeneratedPositionsInflated(s,r)<=0?(this._last=n,this._array.push(n)):(this._sorted=!1,this._array.push(n))},t.prototype.toArray=function(){return this._sorted||(this._array.sort(e.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},Ts.MappingList=t,Ts}function yo(){if(fo)return Ss;fo=1;var e=al(),t=Kt(),n=ol().ArraySet,s=Qd().MappingList;function r(a){a||(a={}),this._file=t.getArg(a,"file",null),this._sourceRoot=t.getArg(a,"sourceRoot",null),this._skipValidation=t.getArg(a,"skipValidation",!1),this._sources=new n,this._names=new n,this._mappings=new s,this._sourcesContents=null}return r.prototype._version=3,r.fromSourceMap=function(a){var o=a.sourceRoot,i=new r({file:a.file,sourceRoot:o});return a.eachMapping(function(c){var l={generated:{line:c.generatedLine,column:c.generatedColumn}};c.source!=null&&(l.source=c.source,o!=null&&(l.source=t.relative(o,l.source)),l.original={line:c.originalLine,column:c.originalColumn},c.name!=null&&(l.name=c.name)),i.addMapping(l)}),a.sources.forEach(function(c){var l=c;o!==null&&(l=t.relative(o,c)),i._sources.has(l)||i._sources.add(l);var d=a.sourceContentFor(c);d!=null&&i.setSourceContent(c,d)}),i},r.prototype.addMapping=function(a){var o=t.getArg(a,"generated"),i=t.getArg(a,"original",null),c=t.getArg(a,"source",null),l=t.getArg(a,"name",null);this._skipValidation||this._validateMapping(o,i,c,l),c!=null&&(c=String(c),this._sources.has(c)||this._sources.add(c)),l!=null&&(l=String(l),this._names.has(l)||this._names.add(l)),this._mappings.add({generatedLine:o.line,generatedColumn:o.column,originalLine:i!=null&&i.line,originalColumn:i!=null&&i.column,source:c,name:l})},r.prototype.setSourceContent=function(a,o){var i=a;this._sourceRoot!=null&&(i=t.relative(this._sourceRoot,i)),o!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[t.toSetString(i)]=o):this._sourcesContents&&(delete this._sourcesContents[t.toSetString(i)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))},r.prototype.applySourceMap=function(a,o,i){var c=o;if(o==null){if(a.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);c=a.file}var l=this._sourceRoot;l!=null&&(c=t.relative(l,c));var d=new n,u=new n;this._mappings.unsortedForEach(function(h){if(h.source===c&&h.originalLine!=null){var p=a.originalPositionFor({line:h.originalLine,column:h.originalColumn});p.source!=null&&(h.source=p.source,i!=null&&(h.source=t.join(i,h.source)),l!=null&&(h.source=t.relative(l,h.source)),h.originalLine=p.line,h.originalColumn=p.column,p.name!=null&&(h.name=p.name))}var m=h.source;m==null||d.has(m)||d.add(m);var y=h.name;y==null||u.has(y)||u.add(y)},this),this._sources=d,this._names=u,a.sources.forEach(function(h){var p=a.sourceContentFor(h);p!=null&&(i!=null&&(h=t.join(i,h)),l!=null&&(h=t.relative(l,h)),this.setSourceContent(h,p))},this)},r.prototype._validateMapping=function(a,o,i,c){if(o&&typeof o.line!="number"&&typeof o.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(a&&"line"in a&&"column"in a&&a.line>0&&a.column>=0)||o||i||c)&&!(a&&"line"in a&&"column"in a&&o&&"line"in o&&"column"in o&&a.line>0&&a.column>=0&&o.line>0&&o.column>=0&&i))throw new Error("Invalid mapping: "+JSON.stringify({generated:a,source:i,original:o,name:c}))},r.prototype._serializeMappings=function(){for(var a,o,i,c,l=0,d=1,u=0,h=0,p=0,m=0,y="",f=this._mappings.toArray(),b=0,S=f.length;b<S;b++){if(a="",(o=f[b]).generatedLine!==d)for(l=0;o.generatedLine!==d;)a+=";",d++;else if(b>0){if(!t.compareByGeneratedPositionsInflated(o,f[b-1]))continue;a+=","}a+=e.encode(o.generatedColumn-l),l=o.generatedColumn,o.source!=null&&(c=this._sources.indexOf(o.source),a+=e.encode(c-m),m=c,a+=e.encode(o.originalLine-1-h),h=o.originalLine-1,a+=e.encode(o.originalColumn-u),u=o.originalColumn,o.name!=null&&(i=this._names.indexOf(o.name),a+=e.encode(i-p),p=i)),y+=a}return y},r.prototype._generateSourcesContent=function(a,o){return a.map(function(i){if(!this._sourcesContents)return null;o!=null&&(i=t.relative(o,i));var c=t.toSetString(i);return Object.prototype.hasOwnProperty.call(this._sourcesContents,c)?this._sourcesContents[c]:null},this)},r.prototype.toJSON=function(){var a={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(a.file=this._file),this._sourceRoot!=null&&(a.sourceRoot=this._sourceRoot),this._sourcesContents&&(a.sourcesContent=this._generateSourcesContent(a.sources,a.sourceRoot)),a},r.prototype.toString=function(){return JSON.stringify(this.toJSON())},Ss.SourceMapGenerator=r,Ss}var _o,It={},bo={};function eh(){return _o||(_o=1,function(e){function t(n,s,r,a,o,i){var c=Math.floor((s-n)/2)+n,l=o(r,a[c],!0);return l===0?c:l>0?s-c>1?t(c,s,r,a,o,i):i==e.LEAST_UPPER_BOUND?s<a.length?s:-1:c:c-n>1?t(n,c,r,a,o,i):i==e.LEAST_UPPER_BOUND?c:n<0?-1:n}e.GREATEST_LOWER_BOUND=1,e.LEAST_UPPER_BOUND=2,e.search=function(n,s,r,a){if(s.length===0)return-1;var o=t(-1,s.length,n,s,r,a||e.GREATEST_LOWER_BOUND);if(o<0)return-1;for(;o-1>=0&&r(s[o],s[o-1],!0)===0;)--o;return o}}(bo)),bo}var vo,So,ws={};function th(){if(vo)return ws;function e(n,s,r){var a=n[s];n[s]=n[r],n[r]=a}function t(n,s,r,a){if(r<a){var o=r-1;e(n,(d=r,u=a,Math.round(d+Math.random()*(u-d))),a);for(var i=n[a],c=r;c<a;c++)s(n[c],i)<=0&&e(n,o+=1,c);e(n,o+1,c);var l=o+1;t(n,s,r,l-1),t(n,s,l+1,a)}var d,u}return vo=1,ws.quickSort=function(n,s){t(n,s,0,n.length-1)},ws}var Eo,To,Is={};function nh(){return To||(To=1,on.SourceMapGenerator=yo().SourceMapGenerator,on.SourceMapConsumer=function(){if(So)return It;So=1;var e=Kt(),t=eh(),n=ol().ArraySet,s=al(),r=th().quickSort;function a(l,d){var u=l;return typeof l=="string"&&(u=e.parseSourceMapInput(l)),u.sections!=null?new c(u,d):new o(u,d)}function o(l,d){var u=l;typeof l=="string"&&(u=e.parseSourceMapInput(l));var h=e.getArg(u,"version"),p=e.getArg(u,"sources"),m=e.getArg(u,"names",[]),y=e.getArg(u,"sourceRoot",null),f=e.getArg(u,"sourcesContent",null),b=e.getArg(u,"mappings"),S=e.getArg(u,"file",null);if(h!=this._version)throw new Error("Unsupported version: "+h);y&&(y=e.normalize(y)),p=p.map(String).map(e.normalize).map(function(v){return y&&e.isAbsolute(y)&&e.isAbsolute(v)?e.relative(y,v):v}),this._names=n.fromArray(m.map(String),!0),this._sources=n.fromArray(p,!0),this._absoluteSources=this._sources.toArray().map(function(v){return e.computeSourceURL(y,v,d)}),this.sourceRoot=y,this.sourcesContent=f,this._mappings=b,this._sourceMapURL=d,this.file=S}function i(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function c(l,d){var u=l;typeof l=="string"&&(u=e.parseSourceMapInput(l));var h=e.getArg(u,"version"),p=e.getArg(u,"sections");if(h!=this._version)throw new Error("Unsupported version: "+h);this._sources=new n,this._names=new n;var m={line:-1,column:0};this._sections=p.map(function(y){if(y.url)throw new Error("Support for url field in sections not implemented.");var f=e.getArg(y,"offset"),b=e.getArg(f,"line"),S=e.getArg(f,"column");if(b<m.line||b===m.line&&S<m.column)throw new Error("Section offsets must be ordered and non-overlapping.");return m=f,{generatedOffset:{generatedLine:b+1,generatedColumn:S+1},consumer:new a(e.getArg(y,"map"),d)}})}return a.fromSourceMap=function(l,d){return o.fromSourceMap(l,d)},a.prototype._version=3,a.prototype.__generatedMappings=null,Object.defineProperty(a.prototype,"_generatedMappings",{configurable:!0,enumerable:!0,get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),a.prototype.__originalMappings=null,Object.defineProperty(a.prototype,"_originalMappings",{configurable:!0,enumerable:!0,get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),a.prototype._charIsMappingSeparator=function(l,d){var u=l.charAt(d);return u===";"||u===","},a.prototype._parseMappings=function(l,d){throw new Error("Subclasses must implement _parseMappings")},a.GENERATED_ORDER=1,a.ORIGINAL_ORDER=2,a.GREATEST_LOWER_BOUND=1,a.LEAST_UPPER_BOUND=2,a.prototype.eachMapping=function(l,d,u){var h,p=d||null;switch(u||a.GENERATED_ORDER){case a.GENERATED_ORDER:h=this._generatedMappings;break;case a.ORIGINAL_ORDER:h=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var m=this.sourceRoot;h.map(function(y){var f=y.source===null?null:this._sources.at(y.source);return{source:f=e.computeSourceURL(m,f,this._sourceMapURL),generatedLine:y.generatedLine,generatedColumn:y.generatedColumn,originalLine:y.originalLine,originalColumn:y.originalColumn,name:y.name===null?null:this._names.at(y.name)}},this).forEach(l,p)},a.prototype.allGeneratedPositionsFor=function(l){var d=e.getArg(l,"line"),u={source:e.getArg(l,"source"),originalLine:d,originalColumn:e.getArg(l,"column",0)};if(u.source=this._findSourceIndex(u.source),u.source<0)return[];var h=[],p=this._findMapping(u,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,t.LEAST_UPPER_BOUND);if(p>=0){var m=this._originalMappings[p];if(l.column===void 0)for(var y=m.originalLine;m&&m.originalLine===y;)h.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++p];else for(var f=m.originalColumn;m&&m.originalLine===d&&m.originalColumn==f;)h.push({line:e.getArg(m,"generatedLine",null),column:e.getArg(m,"generatedColumn",null),lastColumn:e.getArg(m,"lastGeneratedColumn",null)}),m=this._originalMappings[++p]}return h},It.SourceMapConsumer=a,o.prototype=Object.create(a.prototype),o.prototype.consumer=a,o.prototype._findSourceIndex=function(l){var d,u=l;if(this.sourceRoot!=null&&(u=e.relative(this.sourceRoot,u)),this._sources.has(u))return this._sources.indexOf(u);for(d=0;d<this._absoluteSources.length;++d)if(this._absoluteSources[d]==l)return d;return-1},o.fromSourceMap=function(l,d){var u=Object.create(o.prototype),h=u._names=n.fromArray(l._names.toArray(),!0),p=u._sources=n.fromArray(l._sources.toArray(),!0);u.sourceRoot=l._sourceRoot,u.sourcesContent=l._generateSourcesContent(u._sources.toArray(),u.sourceRoot),u.file=l._file,u._sourceMapURL=d,u._absoluteSources=u._sources.toArray().map(function(w){return e.computeSourceURL(u.sourceRoot,w,d)});for(var m=l._mappings.toArray().slice(),y=u.__generatedMappings=[],f=u.__originalMappings=[],b=0,S=m.length;b<S;b++){var v=m[b],T=new i;T.generatedLine=v.generatedLine,T.generatedColumn=v.generatedColumn,v.source&&(T.source=p.indexOf(v.source),T.originalLine=v.originalLine,T.originalColumn=v.originalColumn,v.name&&(T.name=h.indexOf(v.name)),f.push(T)),y.push(T)}return r(u.__originalMappings,e.compareByOriginalPositions),u},o.prototype._version=3,Object.defineProperty(o.prototype,"sources",{get:function(){return this._absoluteSources.slice()}}),o.prototype._parseMappings=function(l,d){for(var u,h,p,m,y,f=1,b=0,S=0,v=0,T=0,w=0,I=l.length,k=0,U={},D={},N=[],X=[];k<I;)if(l.charAt(k)===";")f++,k++,b=0;else if(l.charAt(k)===",")k++;else{for((u=new i).generatedLine=f,m=k;m<I&&!this._charIsMappingSeparator(l,m);m++);if(p=U[h=l.slice(k,m)])k+=h.length;else{for(p=[];k<m;)s.decode(l,k,D),y=D.value,k=D.rest,p.push(y);if(p.length===2)throw new Error("Found a source, but no line and column");if(p.length===3)throw new Error("Found a source and line, but no column");U[h]=p}u.generatedColumn=b+p[0],b=u.generatedColumn,p.length>1&&(u.source=T+p[1],T+=p[1],u.originalLine=S+p[2],S=u.originalLine,u.originalLine+=1,u.originalColumn=v+p[3],v=u.originalColumn,p.length>4&&(u.name=w+p[4],w+=p[4])),X.push(u),typeof u.originalLine=="number"&&N.push(u)}r(X,e.compareByGeneratedPositionsDeflated),this.__generatedMappings=X,r(N,e.compareByOriginalPositions),this.__originalMappings=N},o.prototype._findMapping=function(l,d,u,h,p,m){if(l[u]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+l[u]);if(l[h]<0)throw new TypeError("Column must be greater than or equal to 0, got "+l[h]);return t.search(l,d,p,m)},o.prototype.computeColumnSpans=function(){for(var l=0;l<this._generatedMappings.length;++l){var d=this._generatedMappings[l];if(l+1<this._generatedMappings.length){var u=this._generatedMappings[l+1];if(d.generatedLine===u.generatedLine){d.lastGeneratedColumn=u.generatedColumn-1;continue}}d.lastGeneratedColumn=1/0}},o.prototype.originalPositionFor=function(l){var d={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},u=this._findMapping(d,this._generatedMappings,"generatedLine","generatedColumn",e.compareByGeneratedPositionsDeflated,e.getArg(l,"bias",a.GREATEST_LOWER_BOUND));if(u>=0){var h=this._generatedMappings[u];if(h.generatedLine===d.generatedLine){var p=e.getArg(h,"source",null);p!==null&&(p=this._sources.at(p),p=e.computeSourceURL(this.sourceRoot,p,this._sourceMapURL));var m=e.getArg(h,"name",null);return m!==null&&(m=this._names.at(m)),{source:p,line:e.getArg(h,"originalLine",null),column:e.getArg(h,"originalColumn",null),name:m}}}return{source:null,line:null,column:null,name:null}},o.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(l){return l==null})},o.prototype.sourceContentFor=function(l,d){if(!this.sourcesContent)return null;var u=this._findSourceIndex(l);if(u>=0)return this.sourcesContent[u];var h,p=l;if(this.sourceRoot!=null&&(p=e.relative(this.sourceRoot,p)),this.sourceRoot!=null&&(h=e.urlParse(this.sourceRoot))){var m=p.replace(/^file:\/\//,"");if(h.scheme=="file"&&this._sources.has(m))return this.sourcesContent[this._sources.indexOf(m)];if((!h.path||h.path=="/")&&this._sources.has("/"+p))return this.sourcesContent[this._sources.indexOf("/"+p)]}if(d)return null;throw new Error('"'+p+'" is not in the SourceMap.')},o.prototype.generatedPositionFor=function(l){var d=e.getArg(l,"source");if((d=this._findSourceIndex(d))<0)return{line:null,column:null,lastColumn:null};var u={source:d,originalLine:e.getArg(l,"line"),originalColumn:e.getArg(l,"column")},h=this._findMapping(u,this._originalMappings,"originalLine","originalColumn",e.compareByOriginalPositions,e.getArg(l,"bias",a.GREATEST_LOWER_BOUND));if(h>=0){var p=this._originalMappings[h];if(p.source===u.source)return{line:e.getArg(p,"generatedLine",null),column:e.getArg(p,"generatedColumn",null),lastColumn:e.getArg(p,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},It.BasicSourceMapConsumer=o,c.prototype=Object.create(a.prototype),c.prototype.constructor=a,c.prototype._version=3,Object.defineProperty(c.prototype,"sources",{get:function(){for(var l=[],d=0;d<this._sections.length;d++)for(var u=0;u<this._sections[d].consumer.sources.length;u++)l.push(this._sections[d].consumer.sources[u]);return l}}),c.prototype.originalPositionFor=function(l){var d={generatedLine:e.getArg(l,"line"),generatedColumn:e.getArg(l,"column")},u=t.search(d,this._sections,function(p,m){return p.generatedLine-m.generatedOffset.generatedLine||p.generatedColumn-m.generatedOffset.generatedColumn}),h=this._sections[u];return h?h.consumer.originalPositionFor({line:d.generatedLine-(h.generatedOffset.generatedLine-1),column:d.generatedColumn-(h.generatedOffset.generatedLine===d.generatedLine?h.generatedOffset.generatedColumn-1:0),bias:l.bias}):{source:null,line:null,column:null,name:null}},c.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(l){return l.consumer.hasContentsOfAllSources()})},c.prototype.sourceContentFor=function(l,d){for(var u=0;u<this._sections.length;u++){var h=this._sections[u].consumer.sourceContentFor(l,!0);if(h)return h}if(d)return null;throw new Error('"'+l+'" is not in the SourceMap.')},c.prototype.generatedPositionFor=function(l){for(var d=0;d<this._sections.length;d++){var u=this._sections[d];if(u.consumer._findSourceIndex(e.getArg(l,"source"))!==-1){var h=u.consumer.generatedPositionFor(l);if(h)return{line:h.line+(u.generatedOffset.generatedLine-1),column:h.column+(u.generatedOffset.generatedLine===h.line?u.generatedOffset.generatedColumn-1:0)}}}return{line:null,column:null}},c.prototype._parseMappings=function(l,d){this.__generatedMappings=[],this.__originalMappings=[];for(var u=0;u<this._sections.length;u++)for(var h=this._sections[u],p=h.consumer._generatedMappings,m=0;m<p.length;m++){var y=p[m],f=h.consumer._sources.at(y.source);f=e.computeSourceURL(h.consumer.sourceRoot,f,this._sourceMapURL),this._sources.add(f),f=this._sources.indexOf(f);var b=null;y.name&&(b=h.consumer._names.at(y.name),this._names.add(b),b=this._names.indexOf(b));var S={source:f,generatedLine:y.generatedLine+(h.generatedOffset.generatedLine-1),generatedColumn:y.generatedColumn+(h.generatedOffset.generatedLine===y.generatedLine?h.generatedOffset.generatedColumn-1:0),originalLine:y.originalLine,originalColumn:y.originalColumn,name:b};this.__generatedMappings.push(S),typeof S.originalLine=="number"&&this.__originalMappings.push(S)}r(this.__generatedMappings,e.compareByGeneratedPositionsDeflated),r(this.__originalMappings,e.compareByOriginalPositions)},It.IndexedSourceMapConsumer=c,It}().SourceMapConsumer,on.SourceNode=function(){if(Eo)return Is;Eo=1;var e=yo().SourceMapGenerator,t=Kt(),n=/(\r?\n)/,s="$$$isSourceNode$$$";function r(a,o,i,c,l){this.children=[],this.sourceContents={},this.line=a??null,this.column=o??null,this.source=i??null,this.name=l??null,this[s]=!0,c!=null&&this.add(c)}return r.fromStringWithSourceMap=function(a,o,i){var c=new r,l=a.split(n),d=0,u=function(){return f()+(f()||"");function f(){return d<l.length?l[d++]:void 0}},h=1,p=0,m=null;return o.eachMapping(function(f){if(m!==null){if(!(h<f.generatedLine)){var b=(S=l[d]||"").substr(0,f.generatedColumn-p);return l[d]=S.substr(f.generatedColumn-p),p=f.generatedColumn,y(m,b),void(m=f)}y(m,u()),h++,p=0}for(;h<f.generatedLine;)c.add(u()),h++;if(p<f.generatedColumn){var S=l[d]||"";c.add(S.substr(0,f.generatedColumn)),l[d]=S.substr(f.generatedColumn),p=f.generatedColumn}m=f},this),d<l.length&&(m&&y(m,u()),c.add(l.splice(d).join(""))),o.sources.forEach(function(f){var b=o.sourceContentFor(f);b!=null&&(i!=null&&(f=t.join(i,f)),c.setSourceContent(f,b))}),c;function y(f,b){if(f===null||f.source===void 0)c.add(b);else{var S=i?t.join(i,f.source):f.source;c.add(new r(f.originalLine,f.originalColumn,S,b,f.name))}}},r.prototype.add=function(a){if(Array.isArray(a))a.forEach(function(o){this.add(o)},this);else{if(!a[s]&&typeof a!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+a);a&&this.children.push(a)}return this},r.prototype.prepend=function(a){if(Array.isArray(a))for(var o=a.length-1;o>=0;o--)this.prepend(a[o]);else{if(!a[s]&&typeof a!="string")throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+a);this.children.unshift(a)}return this},r.prototype.walk=function(a){for(var o,i=0,c=this.children.length;i<c;i++)(o=this.children[i])[s]?o.walk(a):o!==""&&a(o,{source:this.source,line:this.line,column:this.column,name:this.name})},r.prototype.join=function(a){var o,i,c=this.children.length;if(c>0){for(o=[],i=0;i<c-1;i++)o.push(this.children[i]),o.push(a);o.push(this.children[i]),this.children=o}return this},r.prototype.replaceRight=function(a,o){var i=this.children[this.children.length-1];return i[s]?i.replaceRight(a,o):typeof i=="string"?this.children[this.children.length-1]=i.replace(a,o):this.children.push("".replace(a,o)),this},r.prototype.setSourceContent=function(a,o){this.sourceContents[t.toSetString(a)]=o},r.prototype.walkSourceContents=function(a){for(var o=0,i=this.children.length;o<i;o++)this.children[o][s]&&this.children[o].walkSourceContents(a);var c=Object.keys(this.sourceContents);for(o=0,i=c.length;o<i;o++)a(t.fromSetString(c[o]),this.sourceContents[c[o]])},r.prototype.toString=function(){var a="";return this.walk(function(o){a+=o}),a},r.prototype.toStringWithSourceMap=function(a){var o={code:"",line:1,column:0},i=new e(a),c=!1,l=null,d=null,u=null,h=null;return this.walk(function(p,m){o.code+=p,m.source!==null&&m.line!==null&&m.column!==null?(l===m.source&&d===m.line&&u===m.column&&h===m.name||i.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:o.line,column:o.column},name:m.name}),l=m.source,d=m.line,u=m.column,h=m.name,c=!0):c&&(i.addMapping({generated:{line:o.line,column:o.column}}),l=null,c=!1);for(var y=0,f=p.length;y<f;y++)p.charCodeAt(y)===10?(o.line++,o.column=0,y+1===f?(l=null,c=!1):c&&i.addMapping({source:m.source,original:{line:m.line,column:m.column},generated:{line:o.line,column:o.column},name:m.name})):o.column++}),this.walkSourceContents(function(p,m){i.setSourceContent(p,m)}),{code:o.code,map:i}},Is.SourceNode=r,Is}().SourceNode),on}(function(e,t){t.__esModule=!0;var n=P,s=void 0;try{var r=nh();s=r.SourceNode}catch{}function a(i,c,l){if(n.isArray(i)){for(var d=[],u=0,h=i.length;u<h;u++)d.push(c.wrap(i[u],l));return d}return typeof i=="boolean"||typeof i=="number"?i+"":i}function o(i){this.srcFile=i,this.source=[]}s||((s=function(i,c,l,d){this.src="",d&&this.add(d)}).prototype={add:function(i){n.isArray(i)&&(i=i.join("")),this.src+=i},prepend:function(i){n.isArray(i)&&(i=i.join("")),this.src=i+this.src},toStringWithSourceMap:function(){return{code:this.toString()}},toString:function(){return this.src}}),o.prototype={isEmpty:function(){return!this.source.length},prepend:function(i,c){this.source.unshift(this.wrap(i,c))},push:function(i,c){this.source.push(this.wrap(i,c))},merge:function(){var i=this.empty();return this.each(function(c){i.add(["  ",c,`
`])}),i},each:function(i){for(var c=0,l=this.source.length;c<l;c++)i(this.source[c])},empty:function(){var i=this.currentLocation||{start:{}};return new s(i.start.line,i.start.column,this.srcFile)},wrap:function(i){var c=arguments.length<=1||arguments[1]===void 0?this.currentLocation||{start:{}}:arguments[1];return i instanceof s?i:(i=a(i,this,c),new s(c.start.line,c.start.column,this.srcFile,i))},functionCall:function(i,c,l){return l=this.generateList(l),this.wrap([i,c?"."+c+"(":"(",l,")"])},quotedString:function(i){return'"'+(i+"").replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029")+'"'},objectLiteral:function(i){var c=this,l=[];Object.keys(i).forEach(function(u){var h=a(i[u],c);h!=="undefined"&&l.push([c.quotedString(u),":",h])});var d=this.generateList(l);return d.prepend("{"),d.add("}"),d},generateList:function(i){for(var c=this.empty(),l=0,d=i.length;l<d;l++)l&&c.add(","),c.add(a(i[l],this));return c},generateArray:function(i){var c=this.generateList(i);return c.prepend("["),c.add("]"),c}},t.default=o,e.exports=t.default})(gr,gr.exports);var sh=gr.exports;(function(e,t){function n(l){return l&&l.__esModule?l:{default:l}}t.__esModule=!0;var s=ge,r=n(Te),a=P,o=n(sh);function i(l){this.value=l}function c(){}c.prototype={nameLookup:function(l,d){return this.internalNameLookup(l,d)},depthedLookup:function(l){return[this.aliasable("container.lookup"),"(depths, ",JSON.stringify(l),")"]},compilerInfo:function(){var l=s.COMPILER_REVISION;return[l,s.REVISION_CHANGES[l]]},appendToBuffer:function(l,d,u){return a.isArray(l)||(l=[l]),l=this.source.wrap(l,d),this.environment.isSimple?["return ",l,";"]:u?["buffer += ",l,";"]:(l.appendToBuffer=!0,l)},initializeBuffer:function(){return this.quotedString("")},internalNameLookup:function(l,d){return this.lookupPropertyFunctionIsUsed=!0,["lookupProperty(",l,",",JSON.stringify(d),")"]},lookupPropertyFunctionIsUsed:!1,compile:function(l,d,u,h){this.environment=l,this.options=d,this.stringParams=this.options.stringParams,this.trackIds=this.options.trackIds,this.precompile=!h,this.name=this.environment.name,this.isChild=!!u,this.context=u||{decorators:[],programs:[],environments:[]},this.preamble(),this.stackSlot=0,this.stackVars=[],this.aliases={},this.registers={list:[]},this.hashes=[],this.compileStack=[],this.inlineStack=[],this.blockParams=[],this.compileChildren(l,d),this.useDepths=this.useDepths||l.useDepths||l.useDecorators||this.options.compat,this.useBlockParams=this.useBlockParams||l.useBlockParams;var p=l.opcodes,m=void 0,y=void 0,f=void 0,b=void 0;for(f=0,b=p.length;f<b;f++)m=p[f],this.source.currentLocation=m.loc,y=y||m.loc,this[m.opcode].apply(this,m.args);if(this.source.currentLocation=y,this.pushSource(""),this.stackSlot||this.inlineStack.length||this.compileStack.length)throw new r.default("Compile completed with content left on stack");this.decorators.isEmpty()?this.decorators=void 0:(this.useDecorators=!0,this.decorators.prepend(["var decorators = container.decorators, ",this.lookupPropertyFunctionVarDeclaration(),`;
`]),this.decorators.push("return fn;"),h?this.decorators=Function.apply(this,["fn","props","container","depth0","data","blockParams","depths",this.decorators.merge()]):(this.decorators.prepend(`function(fn, props, container, depth0, data, blockParams, depths) {
`),this.decorators.push(`}
`),this.decorators=this.decorators.merge()));var S=this.createFunctionContext(h);if(this.isChild)return S;var v={compiler:this.compilerInfo(),main:S};this.decorators&&(v.main_d=this.decorators,v.useDecorators=!0);var T=this.context,w=T.programs,I=T.decorators;for(f=0,b=w.length;f<b;f++)w[f]&&(v[f]=w[f],I[f]&&(v[f+"_d"]=I[f],v.useDecorators=!0));return this.environment.usePartial&&(v.usePartial=!0),this.options.data&&(v.useData=!0),this.useDepths&&(v.useDepths=!0),this.useBlockParams&&(v.useBlockParams=!0),this.options.compat&&(v.compat=!0),h?v.compilerOptions=this.options:(v.compiler=JSON.stringify(v.compiler),this.source.currentLocation={start:{line:1,column:0}},v=this.objectLiteral(v),d.srcName?(v=v.toStringWithSourceMap({file:d.destName})).map=v.map&&v.map.toString():v=v.toString()),v},preamble:function(){this.lastContext=0,this.source=new o.default(this.options.srcName),this.decorators=new o.default(this.options.srcName)},createFunctionContext:function(l){var d=this,u="",h=this.stackVars.concat(this.registers.list);h.length>0&&(u+=", "+h.join(", "));var p=0;Object.keys(this.aliases).forEach(function(f){var b=d.aliases[f];b.children&&b.referenceCount>1&&(u+=", alias"+ ++p+"="+f,b.children[0]="alias"+p)}),this.lookupPropertyFunctionIsUsed&&(u+=", "+this.lookupPropertyFunctionVarDeclaration());var m=["container","depth0","helpers","partials","data"];(this.useBlockParams||this.useDepths)&&m.push("blockParams"),this.useDepths&&m.push("depths");var y=this.mergeSource(u);return l?(m.push(y),Function.apply(this,m)):this.source.wrap(["function(",m.join(","),`) {
  `,y,"}"])},mergeSource:function(l){var d=this.environment.isSimple,u=!this.forceBuffer,h=void 0,p=void 0,m=void 0,y=void 0;return this.source.each(function(f){f.appendToBuffer?(m?f.prepend("  + "):m=f,y=f):(m&&(p?m.prepend("buffer += "):h=!0,y.add(";"),m=y=void 0),p=!0,d||(u=!1))}),u?m?(m.prepend("return "),y.add(";")):p||this.source.push('return "";'):(l+=", buffer = "+(h?"":this.initializeBuffer()),m?(m.prepend("return buffer + "),y.add(";")):this.source.push("return buffer;")),l&&this.source.prepend("var "+l.substring(2)+(h?"":`;
`)),this.source.merge()},lookupPropertyFunctionVarDeclaration:function(){return`
      lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    }
    `.trim()},blockValue:function(l){var d=this.aliasable("container.hooks.blockHelperMissing"),u=[this.contextName(0)];this.setupHelperArgs(l,0,u);var h=this.popStack();u.splice(1,0,h),this.push(this.source.functionCall(d,"call",u))},ambiguousBlockValue:function(){var l=this.aliasable("container.hooks.blockHelperMissing"),d=[this.contextName(0)];this.setupHelperArgs("",0,d,!0),this.flushInline();var u=this.topStack();d.splice(1,0,u),this.pushSource(["if (!",this.lastHelper,") { ",u," = ",this.source.functionCall(l,"call",d),"}"])},appendContent:function(l){this.pendingContent?l=this.pendingContent+l:this.pendingLocation=this.source.currentLocation,this.pendingContent=l},append:function(){if(this.isInline())this.replaceStack(function(d){return[" != null ? ",d,' : ""']}),this.pushSource(this.appendToBuffer(this.popStack()));else{var l=this.popStack();this.pushSource(["if (",l," != null) { ",this.appendToBuffer(l,void 0,!0)," }"]),this.environment.isSimple&&this.pushSource(["else { ",this.appendToBuffer("''",void 0,!0)," }"])}},appendEscaped:function(){this.pushSource(this.appendToBuffer([this.aliasable("container.escapeExpression"),"(",this.popStack(),")"]))},getContext:function(l){this.lastContext=l},pushContext:function(){this.pushStackLiteral(this.contextName(this.lastContext))},lookupOnContext:function(l,d,u,h){var p=0;h||!this.options.compat||this.lastContext?this.pushContext():this.push(this.depthedLookup(l[p++])),this.resolvePath("context",l,p,d,u)},lookupBlockParam:function(l,d){this.useBlockParams=!0,this.push(["blockParams[",l[0],"][",l[1],"]"]),this.resolvePath("context",d,1)},lookupData:function(l,d,u){l?this.pushStackLiteral("container.data(data, "+l+")"):this.pushStackLiteral("data"),this.resolvePath("data",d,0,!0,u)},resolvePath:function(l,d,u,h,p){var m=this;if(this.options.strict||this.options.assumeObjects)this.push(function(f,b,S,v,T){var w=b.popStack(),I=S.length;for(f&&I--;v<I;v++)w=b.nameLookup(w,S[v],T);return f?[b.aliasable("container.strict"),"(",w,", ",b.quotedString(S[v]),", ",JSON.stringify(b.source.currentLocation)," )"]:w}(this.options.strict&&p,this,d,u,l));else for(var y=d.length;u<y;u++)this.replaceStack(function(f){var b=m.nameLookup(f,d[u],l);return h?[" && ",b]:[" != null ? ",b," : ",f]})},resolvePossibleLambda:function(){this.push([this.aliasable("container.lambda"),"(",this.popStack(),", ",this.contextName(0),")"])},pushStringParam:function(l,d){this.pushContext(),this.pushString(d),d!=="SubExpression"&&(typeof l=="string"?this.pushString(l):this.pushStackLiteral(l))},emptyHash:function(l){this.trackIds&&this.push("{}"),this.stringParams&&(this.push("{}"),this.push("{}")),this.pushStackLiteral(l?"undefined":"{}")},pushHash:function(){this.hash&&this.hashes.push(this.hash),this.hash={values:{},types:[],contexts:[],ids:[]}},popHash:function(){var l=this.hash;this.hash=this.hashes.pop(),this.trackIds&&this.push(this.objectLiteral(l.ids)),this.stringParams&&(this.push(this.objectLiteral(l.contexts)),this.push(this.objectLiteral(l.types))),this.push(this.objectLiteral(l.values))},pushString:function(l){this.pushStackLiteral(this.quotedString(l))},pushLiteral:function(l){this.pushStackLiteral(l)},pushProgram:function(l){l!=null?this.pushStackLiteral(this.programExpression(l)):this.pushStackLiteral(null)},registerDecorator:function(l,d){var u=this.nameLookup("decorators",d,"decorator"),h=this.setupHelperArgs(d,l);this.decorators.push(["fn = ",this.decorators.functionCall(u,"",["fn","props","container",h])," || fn;"])},invokeHelper:function(l,d,u){var h=this.popStack(),p=this.setupHelper(l,d),m=[];u&&m.push(p.name),m.push(h),this.options.strict||m.push(this.aliasable("container.hooks.helperMissing"));var y=["(",this.itemsSeparatedBy(m,"||"),")"],f=this.source.functionCall(y,"call",p.callParams);this.push(f)},itemsSeparatedBy:function(l,d){var u=[];u.push(l[0]);for(var h=1;h<l.length;h++)u.push(d,l[h]);return u},invokeKnownHelper:function(l,d){var u=this.setupHelper(l,d);this.push(this.source.functionCall(u.name,"call",u.callParams))},invokeAmbiguous:function(l,d){this.useRegister("helper");var u=this.popStack();this.emptyHash();var h=this.setupHelper(0,l,d),p=["(","(helper = ",this.lastHelper=this.nameLookup("helpers",l,"helper")," || ",u,")"];this.options.strict||(p[0]="(helper = ",p.push(" != null ? helper : ",this.aliasable("container.hooks.helperMissing"))),this.push(["(",p,h.paramsInit?["),(",h.paramsInit]:[],"),","(typeof helper === ",this.aliasable('"function"')," ? ",this.source.functionCall("helper","call",h.callParams)," : helper))"])},invokePartial:function(l,d,u){var h=[],p=this.setupParams(d,1,h);l&&(d=this.popStack(),delete p.name),u&&(p.indent=JSON.stringify(u)),p.helpers="helpers",p.partials="partials",p.decorators="container.decorators",l?h.unshift(d):h.unshift(this.nameLookup("partials",d,"partial")),this.options.compat&&(p.depths="depths"),p=this.objectLiteral(p),h.push(p),this.push(this.source.functionCall("container.invokePartial","",h))},assignToHash:function(l){var d=this.popStack(),u=void 0,h=void 0,p=void 0;this.trackIds&&(p=this.popStack()),this.stringParams&&(h=this.popStack(),u=this.popStack());var m=this.hash;u&&(m.contexts[l]=u),h&&(m.types[l]=h),p&&(m.ids[l]=p),m.values[l]=d},pushId:function(l,d,u){l==="BlockParam"?this.pushStackLiteral("blockParams["+d[0]+"].path["+d[1]+"]"+(u?" + "+JSON.stringify("."+u):"")):l==="PathExpression"?this.pushString(d):l==="SubExpression"?this.pushStackLiteral("true"):this.pushStackLiteral("null")},compiler:c,compileChildren:function(l,d){for(var u=l.children,h=void 0,p=void 0,m=0,y=u.length;m<y;m++){h=u[m],p=new this.compiler;var f=this.matchExistingProgram(h);if(f==null){this.context.programs.push("");var b=this.context.programs.length;h.index=b,h.name="program"+b,this.context.programs[b]=p.compile(h,d,this.context,!this.precompile),this.context.decorators[b]=p.decorators,this.context.environments[b]=h,this.useDepths=this.useDepths||p.useDepths,this.useBlockParams=this.useBlockParams||p.useBlockParams,h.useDepths=this.useDepths,h.useBlockParams=this.useBlockParams}else h.index=f.index,h.name="program"+f.index,this.useDepths=this.useDepths||f.useDepths,this.useBlockParams=this.useBlockParams||f.useBlockParams}},matchExistingProgram:function(l){for(var d=0,u=this.context.environments.length;d<u;d++){var h=this.context.environments[d];if(h&&h.equals(l))return h}},programExpression:function(l){var d=this.environment.children[l],u=[d.index,"data",d.blockParams];return(this.useBlockParams||this.useDepths)&&u.push("blockParams"),this.useDepths&&u.push("depths"),"container.program("+u.join(", ")+")"},useRegister:function(l){this.registers[l]||(this.registers[l]=!0,this.registers.list.push(l))},push:function(l){return l instanceof i||(l=this.source.wrap(l)),this.inlineStack.push(l),l},pushStackLiteral:function(l){this.push(new i(l))},pushSource:function(l){this.pendingContent&&(this.source.push(this.appendToBuffer(this.source.quotedString(this.pendingContent),this.pendingLocation)),this.pendingContent=void 0),l&&this.source.push(l)},replaceStack:function(l){var d=["("],u=void 0,h=void 0,p=void 0;if(!this.isInline())throw new r.default("replaceStack on non-inline");var m=this.popStack(!0);if(m instanceof i)d=["(",u=[m.value]],p=!0;else{h=!0;var y=this.incrStack();d=["((",this.push(y)," = ",m,")"],u=this.topStack()}var f=l.call(this,u);p||this.popStack(),h&&this.stackSlot--,this.push(d.concat(f,")"))},incrStack:function(){return this.stackSlot++,this.stackSlot>this.stackVars.length&&this.stackVars.push("stack"+this.stackSlot),this.topStackName()},topStackName:function(){return"stack"+this.stackSlot},flushInline:function(){var l=this.inlineStack;this.inlineStack=[];for(var d=0,u=l.length;d<u;d++){var h=l[d];if(h instanceof i)this.compileStack.push(h);else{var p=this.incrStack();this.pushSource([p," = ",h,";"]),this.compileStack.push(p)}}},isInline:function(){return this.inlineStack.length},popStack:function(l){var d=this.isInline(),u=(d?this.inlineStack:this.compileStack).pop();if(!l&&u instanceof i)return u.value;if(!d){if(!this.stackSlot)throw new r.default("Invalid stack pop");this.stackSlot--}return u},topStack:function(){var l=this.isInline()?this.inlineStack:this.compileStack,d=l[l.length-1];return d instanceof i?d.value:d},contextName:function(l){return this.useDepths&&l?"depths["+l+"]":"depth"+l},quotedString:function(l){return this.source.quotedString(l)},objectLiteral:function(l){return this.source.objectLiteral(l)},aliasable:function(l){var d=this.aliases[l];return d?(d.referenceCount++,d):((d=this.aliases[l]=this.source.wrap(l)).aliasable=!0,d.referenceCount=1,d)},setupHelper:function(l,d,u){var h=[];return{params:h,paramsInit:this.setupHelperArgs(d,l,h,u),name:this.nameLookup("helpers",d,"helper"),callParams:[this.aliasable(this.contextName(0)+" != null ? "+this.contextName(0)+" : (container.nullContext || {})")].concat(h)}},setupParams:function(l,d,u){var h={},p=[],m=[],y=[],f=!u,b=void 0;f&&(u=[]),h.name=this.quotedString(l),h.hash=this.popStack(),this.trackIds&&(h.hashIds=this.popStack()),this.stringParams&&(h.hashTypes=this.popStack(),h.hashContexts=this.popStack());var S=this.popStack(),v=this.popStack();(v||S)&&(h.fn=v||"container.noop",h.inverse=S||"container.noop");for(var T=d;T--;)b=this.popStack(),u[T]=b,this.trackIds&&(y[T]=this.popStack()),this.stringParams&&(m[T]=this.popStack(),p[T]=this.popStack());return f&&(h.args=this.source.generateArray(u)),this.trackIds&&(h.ids=this.source.generateArray(y)),this.stringParams&&(h.types=this.source.generateArray(m),h.contexts=this.source.generateArray(p)),this.options.data&&(h.data="data"),this.useBlockParams&&(h.blockParams="blockParams"),h},setupHelperArgs:function(l,d,u,h){var p=this.setupParams(l,d,u);return p.loc=JSON.stringify(this.source.currentLocation),p=this.objectLiteral(p),h?(this.useRegister("options"),u.push("options"),["options=",p]):u?(u.push(p),""):p}},function(){for(var l="break else new var case finally return void catch for switch while continue function this with default if throw delete in try do instanceof typeof abstract enum int short boolean export interface static byte extends long super char final native synchronized class float package throws const goto private transient debugger implements protected volatile double import public let yield await null true false".split(" "),d=c.RESERVED_WORDS={},u=0,h=l.length;u<h;u++)d[l[u]]=!0}(),c.isValidJavaScriptVariableName=function(l){return!c.RESERVED_WORDS[l]&&/^[a-zA-Z_$][0-9a-zA-Z_$]*$/.test(l)},t.default=c,e.exports=t.default})(mr,mr.exports);var rh=mr.exports;(function(e,t){function n(p){return p&&p.__esModule?p:{default:p}}t.__esModule=!0;var s=n(jd),r=n(el),a=Ft,o=Rt,i=n(rh),c=n(tl),l=n(Qi),d=s.default.create;function u(){var p=d();return p.compile=function(m,y){return o.compile(m,y,p)},p.precompile=function(m,y){return o.precompile(m,y,p)},p.AST=r.default,p.Compiler=o.Compiler,p.JavaScriptCompiler=i.default,p.Parser=a.parser,p.parse=a.parse,p.parseWithoutProcessing=a.parseWithoutProcessing,p}var h=u();h.create=u,l.default(h),h.Visitor=c.default,h.default=h,t.default=h,e.exports=t.default})(Vs,Vs.exports);const ah=_r(Vs.exports),oh=`
<user>
{{{userMessage}}}
</user>
{{#if hasActions}}
<agent_actions>
{{#if filesModified}}
	<files_modified>
{{#each filesModified}}
		{{{this}}}
{{/each}}
	</files_modified>
{{/if}}
{{#if filesCreated}}
	<files_created>
{{#each filesCreated}}
		{{{this}}}
{{/each}}
	</files_created>
{{/if}}
{{#if filesDeleted}}
	<files_deleted>
{{#each filesDeleted}}
		{{{this}}}
{{/each}}
	</files_deleted>
{{/if}}
{{#if filesViewed}}
	<files_viewed>
{{#each filesViewed}}
		{{{this}}}
{{/each}}
	</files_viewed>
{{/if}}
{{#if terminalCommands}}
	<terminal_commands>
{{#each terminalCommands}}
		{{{this}}}
{{/each}}
	</terminal_commands>
{{/if}}
</agent_actions>
{{/if}}
{{#if agentResponse}}
<agent_response>
{{{agentResponse}}}
</agent_response>
{{else if wasInterrupted}}
<agent_was_interrupted/>
{{else if continues}}
<agent_continues/>
{{/if}}
`.trim(),ih=ah.compile(oh);function lh(e){for(const t of e.filesModified)e.filesViewed.delete(t)}function uh(e,t){try{const n=JSON.parse(e.input_json);switch(e.tool_name){case"str-replace-editor":n.path&&t.filesModified.add(n.path);break;case"save-file":n.path&&t.filesCreated.add(n.path);break;case"remove-files":if(n.file_paths&&Array.isArray(n.file_paths))for(const s of n.file_paths)t.filesDeleted.add(s);break;case"view":n.path&&t.filesViewed.add(n.path);break;case"launch-process":n.command&&t.terminalCommands.add(n.command)}}catch(n){console.warn("Failed to parse tool use input:",n)}}function Nt(e,t,n,s="files"){if(e.size===0)return null;const r=Array.from(e).sort((c,l)=>c.localeCompare(l)),a=c=>Gs(c,n);if(r.length<=t)return r.map(a);const o=r.slice(0,t).map(a),i=r.length-t;return o.push(`... ${i} more ${s}`),o}function ch(e,t){let n=e.userMessage,s=e.agentFinalResponse;n.length>t.userMessageCharsLimit&&(n=Gs(n,t.userMessageCharsLimit)),s.length>t.agentResponseCharsLimit&&(s=Gs(s,t.agentResponseCharsLimit));const r=e.agentActionsSummary.filesModified.size>0||e.agentActionsSummary.filesCreated.size>0||e.agentActionsSummary.filesDeleted.size>0||e.agentActionsSummary.filesViewed.size>0||e.agentActionsSummary.terminalCommands.size>0,a={userMessage:n,agentResponse:s&&s.trim()!==""?s:null,hasActions:r,filesModified:Nt(e.agentActionsSummary.filesModified,t.numFilesModifiedLimit,t.actionCharsLimit),filesCreated:Nt(e.agentActionsSummary.filesCreated,t.numFilesCreatedLimit,t.actionCharsLimit),filesDeleted:Nt(e.agentActionsSummary.filesDeleted,t.numFilesDeletedLimit,t.actionCharsLimit),filesViewed:Nt(e.agentActionsSummary.filesViewed,t.numFilesViewedLimit,t.actionCharsLimit),terminalCommands:Nt(e.agentActionsSummary.terminalCommands,t.numTerminalCommandsLimit,t.actionCharsLimit,"commands"),wasInterrupted:e.wasInterrupted,continues:e.continues};return ih(a)}function dh(e){var n,s;let t=e.request_message||"";return(n=e.structured_request_nodes)!=null&&n.some(r=>r.image_node||r.image_id_node)&&(t+=`
[User attached image]`),(s=e.structured_request_nodes)!=null&&s.some(r=>r.file_node||r.file_id_node)&&(t+=`
[User attached document]`),t}function wo(e){var t,n,s,r,a,o,i,c,l;try{if(!e)return console.log("historySummaryParams is empty. Using default params"),q;const d=JSON.parse(e),u={triggerOnHistorySizeChars:V(d.trigger_on_history_size_chars,q.triggerOnHistorySizeChars),historyTailSizeCharsToExclude:V(d.history_tail_size_chars_to_exclude,q.historyTailSizeCharsToExclude),triggerOnHistorySizeCharsWhenCacheExpiring:V(d.trigger_on_history_size_chars_when_cache_expiring,q.triggerOnHistorySizeCharsWhenCacheExpiring),prompt:V(d.prompt,q.prompt),cacheTTLMs:V(d.cache_ttl_ms,q.cacheTTLMs),bufferTimeBeforeCacheExpirationMs:V(d.buffer_time_before_cache_expiration_ms,q.bufferTimeBeforeCacheExpirationMs),summaryNodeRequestMessageTemplate:V(d.summary_node_request_message_template,q.summaryNodeRequestMessageTemplate),summaryNodeResponseMessage:V(d.summary_node_response_message,q.summaryNodeResponseMessage),abridgedHistoryParams:{totalCharsLimit:V((t=d.abridged_history_params)==null?void 0:t.total_chars_limit,q.abridgedHistoryParams.totalCharsLimit),userMessageCharsLimit:V((n=d.abridged_history_params)==null?void 0:n.user_message_chars_limit,q.abridgedHistoryParams.userMessageCharsLimit),agentResponseCharsLimit:V((s=d.abridged_history_params)==null?void 0:s.agent_response_chars_limit,q.abridgedHistoryParams.agentResponseCharsLimit),actionCharsLimit:V((r=d.abridged_history_params)==null?void 0:r.action_chars_limit,q.abridgedHistoryParams.actionCharsLimit),numFilesModifiedLimit:V((a=d.abridged_history_params)==null?void 0:a.num_files_modified_limit,q.abridgedHistoryParams.numFilesModifiedLimit),numFilesCreatedLimit:V((o=d.abridged_history_params)==null?void 0:o.num_files_created_limit,q.abridgedHistoryParams.numFilesCreatedLimit),numFilesDeletedLimit:V((i=d.abridged_history_params)==null?void 0:i.num_files_deleted_limit,q.abridgedHistoryParams.numFilesDeletedLimit),numFilesViewedLimit:V((c=d.abridged_history_params)==null?void 0:c.num_files_viewed_limit,q.abridgedHistoryParams.numFilesViewedLimit),numTerminalCommandsLimit:V((l=d.abridged_history_params)==null?void 0:l.num_terminal_commands_limit,q.abridgedHistoryParams.numTerminalCommandsLimit)}};u.summaryNodeRequestMessageTemplate.includes("{summary}")||(console.error("summaryNodeRequestMessageTemplate must contain {summary}. Using default template"),u.summaryNodeRequestMessageTemplate=q.summaryNodeRequestMessageTemplate);const h={...u,prompt:u.prompt.slice(0,10)+"..."};return console.log("historySummaryParams updated: ",h),u}catch(d){return console.error("Failed to parse history_summary_params:",d),q}}class hh{constructor(){g(this,"_controllers",new Set);g(this,"_timeoutIds",new Set)}addCallback(t,n){const s=new AbortController,r=setTimeout(()=>{t(s.signal),this._controllers.delete(s),this._timeoutIds.delete(r)},n);this._controllers.add(s),this._timeoutIds.add(r)}cancelAll(){this._controllers.forEach(t=>t.abort()),this._timeoutIds.forEach(t=>clearTimeout(t)),this._controllers.clear(),this._timeoutIds.clear()}}function Ns(e){return e.reduce((t,n)=>t+il(n),0)}function il(e){let t=0;return e.request_nodes?t+=JSON.stringify(e.request_nodes).length:t+=(e.request_message||"").length,e.response_nodes?t+=JSON.stringify(e.response_nodes).length:t+=(e.response_text||"").length,t}class ph{constructor(t,n,s){g(this,"historySummaryVersion",3);g(this,"_callbacksManager",new hh);g(this,"_params");this._conversationModel=t,this._extensionClient=n,this._chatFlagModel=s,this._params=wo(s.historySummaryParams),s.subscribe(r=>{this._params=wo(r.historySummaryParams)})}cancelRunningOrScheduledSummarizations(){this._callbacksManager.cancelAll()}generateAbridgedHistoryText(t){const n=new Set(t.map(o=>o.request_id)),s=function(o){const i=[];let c=null;for(const l of o){if(!H(l))continue;const d=l;if(Th(d)||(c&&(c.agentFinalResponse.trim()===""&&(c.wasInterrupted=!0),i.push(c)),c={userMessage:dh(d),agentActionsSummary:{filesModified:new Set,filesCreated:new Set,filesDeleted:new Set,filesViewed:new Set,terminalCommands:new Set},agentFinalResponse:"",wasInterrupted:!1,continues:!1}),!c)continue;let u=!1;if(d.structured_output_nodes)for(const h of d.structured_output_nodes)h.type===R.TOOL_USE&&h.tool_use&&(u=!0,uh(h.tool_use,c.agentActionsSummary));!u&&d.response_text&&(c.agentFinalResponse=d.response_text)}c&&(c.agentFinalResponse.trim()===""&&(c.continues=!0),i.push(c));for(const l of i)lh(l.agentActionsSummary);return i}(this._conversationModel.chatHistory.filter(o=>o.request_id&&n.has(o.request_id)));let r=0;const a=[];for(let o=s.length-1;o>=0;o--){const i=ch(s[o],this._params.abridgedHistoryParams);if(r+i.length>this._params.abridgedHistoryParams.totalCharsLimit)break;a.push(i),r+=i.length}return a.reverse(),a.join(`
`)}clearStaleHistorySummaryNodes(t){return t.filter(n=>!pt(n)||n.summaryVersion===this.historySummaryVersion)}maybeScheduleSummarization(t){if(!this._chatFlagModel.useHistorySummary||this._params.triggerOnHistorySizeCharsWhenCacheExpiring<=0)return;const n=this._params.cacheTTLMs-t-this._params.bufferTimeBeforeCacheExpirationMs;n>0&&this._callbacksManager.addCallback(s=>{this.maybeAddHistorySummaryNode(!0,s)},n)}preprocessChatHistory(t){const n=t.findLastIndex(s=>pt(s)&&s.summaryVersion===this.historySummaryVersion);return this._chatFlagModel.useHistorySummary?(n>0&&(console.info(`Using history summary node found at index ${n} with requestId: ${t[n].request_id}`),t=t.slice(n)),t=t.filter(s=>!pt(s)||s.summaryVersion===this.historySummaryVersion)):t=t.filter(s=>!pt(s)),t}async maybeAddHistorySummaryNode(t=!1,n){var k,U,D;if(console.log("maybeAddHistorySummaryNode. isCacheAboutToExpire: ",t),!this._params.prompt||this._params.prompt.trim()==="")return console.log("maybeAddHistorySummaryNode. empty prompt"),!1;const s=this._conversationModel.convertHistoryToExchanges(this._conversationModel.chatHistory),r=t?this._params.triggerOnHistorySizeCharsWhenCacheExpiring:this._params.triggerOnHistorySizeChars;if(console.log("maybeAddHistorySummaryNode. maxCharsThreshold: ",r),r<=0)return!1;const{head:a,tail:o,headSizeChars:i,tailSizeChars:c}=function(N,X,at,A){if(N.length===0)return{head:[],tail:[],headSizeChars:0,tailSizeChars:0};const fe=[],B=[];let ye=0,Oe=0,Xt=0;for(let Kn=N.length-1;Kn>=0;Kn--){const Wn=N[Kn],Jt=il(Wn);ye+Jt<X||B.length<A?(B.push(Wn),Xt+=Jt):(fe.push(Wn),Oe+=Jt),ye+=Jt}return ye<at?(B.push(...fe),{head:[],tail:B.reverse(),headSizeChars:0,tailSizeChars:ye}):{head:fe.reverse(),tail:B.reverse(),headSizeChars:Oe,tailSizeChars:Xt}}(s,this._params.historyTailSizeCharsToExclude,r,1);if(console.log("maybeAddHistorySummaryNode. headSizeChars: ",i," tailSizeChars: ",c),a.length===0)return console.log("maybeAddHistorySummaryNode. head is empty. nothing to summarize"),!1;const l=Ns(s),d=Ns(a),u=Ns(o),h={totalHistoryCharCount:l,totalHistoryExchangeCount:s.length,headCharCount:d,headExchangeCount:a.length,headLastRequestId:((k=a.at(-1))==null?void 0:k.request_id)??"",tailCharCount:u,tailExchangeCount:o.length,tailLastRequestId:((U=o.at(-1))==null?void 0:U.request_id)??"",summaryCharCount:0,summarizationDurationMs:0,isCacheAboutToExpire:t,isAborted:!1};let p=((D=a.at(-1))==null?void 0:D.response_nodes)??[],m=p.filter(N=>N.type===R.TOOL_USE);m.length>0&&(a.at(-1).response_nodes=p.filter(N=>N.type!==R.TOOL_USE)),console.info("Summarizing %d turns of conversation history.",a.length);const y=this.generateAbridgedHistoryText(a);console.info("Abridged history text size: %d characters.",y.length);const f=Date.now(),b=await this._conversationModel.sendSilentExchange({request_message:this._params.prompt,disableRetrieval:!0,disableSelectedCodeDetails:!0,chatHistory:a}),S=Date.now();if(console.info("Summary text size: %d characters.",b.responseText.length),h.summaryCharCount=b.responseText.length,h.summarizationDurationMs=S-f,h.isAborted=!!(n!=null&&n.aborted),this._extensionClient.reportAgentRequestEvent({eventName:xn.chatHistorySummarization,conversationId:this._conversationModel.conversationId,requestId:b.requestId??"UNKNOWN_REQUEST_ID",chatHistoryLength:this._conversationModel.chatHistory.length,eventData:{chatHistorySummarizationData:h}}),n==null?void 0:n.aborted)return console.log("maybeAddHistorySummaryNode. aborted"),!1;if(!b.requestId||b.responseText.trim()==="")return console.log("maybeAddHistorySummaryNode. no request id or empty response"),!1;let v=this._params.summaryNodeRequestMessageTemplate.replace("{summary}",`<summary>
${b.responseText}
</summary>`);v.includes("{abridged_history}")&&(v=v.replace("{abridged_history}",`<abridged_history>
${y}
</abridged_history>`));const T=this._params.summaryNodeResponseMessage,w={chatItemType:ht.historySummary,summaryVersion:this.historySummaryVersion,request_id:b.requestId,request_message:v,response_text:T,structured_output_nodes:[{id:m.map(N=>N.id).reduce((N,X)=>Math.max(N,X),-1)+1,type:R.RAW_RESPONSE,content:T},...m],status:C.success,seen_state:te.seen,timestamp:new Date().toISOString()},I=this._conversationModel.chatHistory.findLastIndex(N=>N.request_id===a.at(-1).request_id)+1;return console.info("Adding a history summary node at index %d",I),this._conversationModel.insertChatItem(I,w),!0}}function ll(e){throw new Error("Logger not initialized. Call setLibraryLogger() before using getLogger().")}class cn{constructor(){g(this,"_disposables",[])}add(t){if(t===void 0)throw new Error("Attempt to add undefined disposable to DisposableCollection");return this._disposables.push(t),t}addAll(...t){t.forEach(n=>this.add(n))}adopt(t){this._disposables.push(...t._disposables),t._disposables.length=0}dispose(){for(const t of this._disposables)t.dispose();this._disposables.length=0}}class mh{constructor(t=new cn,n=new cn){g(this,"_disposables",new cn);g(this,"_priorityDisposables",new cn);this._disposables.adopt(t),this._priorityDisposables.adopt(n)}addDisposable(t,n=!1){return n?this._priorityDisposables.add(t):this._disposables.add(t)}addDisposables(...t){this._disposables.addAll(...t)}dispose(){this._priorityDisposables.dispose(),this._disposables.dispose()}}var Ie=(e=>(e[e.Unknown=0]="Unknown",e[e.File=1]="File",e[e.Directory=2]="Directory",e[e.SymbolicLink=64]="SymbolicLink",e))(Ie||{});class ul{static setClientWorkspaces(t){this._instance===void 0?this._instance=t:ll().warn("Attempting to initialize client workspaces when one is already configured. Keeping existing client workspaces.")}static getClientWorkspaces(){if(this._instance===void 0)throw new Error("ClientWorkspaces not set");return this._instance}static reset(){this._instance=void 0}}g(ul,"_instance");const qe=()=>ul.getClientWorkspaces(),gh=[".md",".mdc"],Io=[{directory:".cursor/rules",file:".cursorrules",name:"Cursor"},{directory:".windsurf/rules",file:".windsurfrules",name:"Windsurf"},{directory:".github/instructions",file:".github/copilot-instructions.md",name:"GitHub Copilot"},{directory:".clinerules",file:".clinerules",name:"Cline"},{directory:".roo/rules",file:".roorules",name:"Roo Code"},{directory:".trae/rules",name:"Trae"}];class Br extends mh{constructor(){super();g(this,"_logger",ll())}async loadRules({includeGuidelines:n=!1,query:s,maxResults:r,contextRules:a}={}){this._logger.debug(`Loading rules with includeGuidelines=${n}, query=${s}, maxResults=${r}`),this._logger.debug("Using file system approach to load rules");const o=await this.loadDirectory((void 0)(ut,xt));let i;if(this._logger.debug(`Loaded ${o.length} rules from directory`),n){const c=await this.loadGuidelinesFiles();this._logger.debug(`Loaded ${c.length} guidelines rules`),i=[...c,...o]}else i=o;if(s&&s.trim()){const c=s.toLowerCase().trim();i=i.filter(l=>{const d=l.path.toLowerCase().includes(c),u=l.content.toLowerCase().includes(c);return d||u}),this._logger.debug(`Filtered to ${i.length} rules matching query: ${s}`)}return r&&r>0&&(i=i.slice(0,r),this._logger.debug(`Limited to ${i.length} rules`)),this._logger.debug(`Returning ${i.length} total rules`),a!==void 0&&(i=Br.filterRulesByContext(i,a),this._logger.debug(`Filtered to ${i.length} rules based on context`)),i}static filterRulesByContext(n,s){return[...n.filter(r=>r.type!==ne.MANUAL),...n.filter(r=>r.type===ne.MANUAL&&s.some(a=>a.path===r.path))]}static calculateRulesAndGuidelinesCharacterCount(n){const{rules:s,workspaceGuidelinesContent:r,contextRules:a=[]}=n,o=s.filter(d=>d.type===ne.ALWAYS_ATTACHED).reduce((d,u)=>d+u.content.length+u.path.length,0),i=s.filter(d=>d.type===ne.AGENT_REQUESTED).reduce((d,u)=>{var h;return d+100+(((h=u.description)==null?void 0:h.length)??0)+u.path.length},0),c=o+s.filter(d=>d.type===ne.MANUAL).filter(d=>a.some(u=>u.path===d.path)).reduce((d,u)=>d+u.content.length+u.path.length,0)+i+r.length,l=n.rulesAndGuidelinesLimit&&c>n.rulesAndGuidelinesLimit;return{totalCharacterCount:c,isOverLimit:l,warningMessage:l&&n.rulesAndGuidelinesLimit?`Total number of characters in included rules and workspace guidelines (${c} chars)
        exceeds the limit of ${n.rulesAndGuidelinesLimit} characters, remove some rules
        or reduce the length of your guidelines.`:void 0}}async loadGuidelinesFiles(){const n=[],s=qe();if(!s)return this._logger.warn("Client workspaces not initialized"),n;const r=await s.getWorkspaceRoot();if(!r)return n;const a=(void 0)(r,is),o=await s.getPathInfo(a);if(o.exists&&o.type===Ie.File)try{const i=(await s.readFile(a)).contents;if(!i)return this._logger.warn(`Guidelines file is empty: ${a}`),n;const c=xe.parseRuleFile(i,is);n.push({path:is,content:c.content,type:c.type,description:c.description})}catch(i){this._logger.error(`Error loading guidelines file ${a}: ${String(i)}`)}return n}async loadDirectory(n){const s=[];try{const r=qe();if(!r)return this._logger.warn("Client workspaces not initialized"),s;const a=await r.getWorkspaceRoot();if(!a)return this._logger.warn("No workspace root found"),s;const o=(void 0)(a,n);this._logger.debug(`Looking for rules in: ${o}`);const i=await r.getPathInfo(o);return this._logger.debug(`Path info for ${o}: ${JSON.stringify(i)}`),i.exists&&i.type===Ie.Directory?(this._logger.debug(`Rules folder exists at ${o}`),await this.processRuleDirectory(r,o,s,""),this._logger.debug(`Loaded ${s.length} rules from ${o}`),s):(this._logger.debug(`Rules folder not found at ${o}`),s)}catch(r){return this._logger.error(`Error loading rules: ${String(r)}`),s}}async loadDirectoryFromPath(n){const s=[];try{const r=qe();if(!r)return this._logger.warn("Client workspaces not initialized"),s;let a;if(!(void 0)(n)){const i=await r.getWorkspaceRoot();if(!i)return this._logger.warn("No workspace root found"),s;a=(void 0)(i,n),this._logger.debug(`Loading rules from workspace-relative path: ${a}`)}const o=await r.getPathInfo(a);return o.exists&&o.type===Ie.Directory?(this._logger.debug(`Rules folder exists at ${a}`),await this.processRuleDirectory(r,a,s,""),this._logger.debug(`Loaded ${s.length} rules from ${a}`),s):(this._logger.debug(`Rules folder not found at ${a}`),s)}catch(r){return this._logger.error(`Error loading rules from path: ${String(r)}`),s}}async processRuleDirectory(n,s,r,a){const o=await n.listDirectory(s,1,!1);if(o.errorMessage)this._logger.error(`Error listing directory ${s}: ${o.errorMessage}`);else{this._logger.debug(`Processing directory: ${s}, found ${o.entries.length} entries`);for(const i of o.entries){const c=(void 0)(s,i),l=(void 0)(a,i),d=await n.getPathInfo(c);if(d.exists)if(d.type===Ie.Directory)this._logger.debug(`Processing subdirectory: ${i}`),await this.processRuleDirectory(n,c,r,l);else if(d.type===Ie.File&&gh.some(u=>i.endsWith(u))){this._logger.debug(`Processing rule file: ${i}`);try{const u=(await n.readFile(c)).contents||"",h=xe.parseRuleFile(u,i);r.push({path:l,content:h.content,type:h.type,description:h.description}),this._logger.debug(`Successfully loaded rule: ${l}`)}catch(u){this._logger.error(`Error loading rule file ${c}: ${String(u)}`)}}else d.type===Ie.File&&this._logger.debug(`Skipping non-markdown file: ${i}`)}}}async createRule(n,s=!1){const r=qe();if(!r)throw new Error("Client workspaces not initialized");const a=await r.getWorkspaceRoot();if(!a)throw new Error("No workspace root found");let o=(void 0)(a,ut,xt);s&&(o=(void 0)(o,"imported"));const i=n.path.endsWith(".md")?n.path:`${n.path}.md`,c=(void 0)(o,i),l=await r.getQualifiedPathName(c);if(!l)throw new Error(`Unable to get qualified path for: ${c}`);if((await r.getPathInfo(c)).exists)throw new Error(`Rule file already exists: ${i}`);const d=xe.formatRuleFileForMarkdown(n);return await r.writeFile(l,d),{...n,path:i}}async deleteRule(n){if(typeof n!="string")throw new Error(`Expected rulePath to be a string, got ${typeof n}: ${String(n)}`);const s=qe();if(!s)throw new Error("Client workspaces not initialized");const r=await s.getWorkspaceRoot();if(!r)throw new Error("No workspace root found");let a;if((void 0)(n)||(a=(void 0)(r,ut,xt,n)),(await s.getPathInfo(a)).exists){const o=await s.getQualifiedPathName(a);o&&(await s.deleteFile(o),this._logger.debug(`Deleted rule file: ${a}`)),this._logger.debug(`Deleted rule file: ${a}`)}}async updateRuleFile(n,s){if(typeof n!="string")throw new Error(`Expected rulePath to be a string, got ${typeof n}: ${String(n)}`);const r=qe();if(!r)throw new Error("Client workspaces not initialized");const a=await r.getWorkspaceRoot();if(!a)throw new Error("No workspace root found");let o;(void 0)(n)||(o=n.startsWith(ut)?(void 0)(a,n):(void 0)(a,ut,xt,n));const i=await r.getQualifiedPathName(o);if(!i)throw new Error(`Unable to get qualified path for: ${o}`);await r.writeFile(i,s),this._logger.debug(`Updated rule file: ${o}`)}async importFile(n,s){const r=qe();if(!r)throw new Error("Client workspaces not initialized");let a,o;if(!(void 0)(n)){const c=await r.getWorkspaceRoot();if(!c)throw new Error("No workspace root found");a=(void 0)(c,n),o=n,this._logger.debug(`Importing file from workspace-relative path: ${a}`)}const i=await r.getPathInfo(a);if(!i.exists||i.type!==Ie.File)return this._logger.error(`File not found: ${a}`),{successfulImports:0,duplicates:0,totalAttempted:1};try{const c=(await r.readFile(a)).contents;if(!c)return this._logger.error(`File is empty: ${a}`),{successfulImports:0,duplicates:0,totalAttempted:1};const l=xe.parseRuleFile(c,o),d=(void 0)(o).name.replace(".","");return await this.createRule({path:d,content:l.content,type:l.type},s),{successfulImports:1,duplicates:0,totalAttempted:1}}catch(c){return this._logger.error(`Error importing file ${n}: ${String(c)}`),{successfulImports:0,duplicates:String(c).includes("already exists")?1:0,totalAttempted:1}}}async importDirectory(n,s){try{const r=await this.loadDirectoryFromPath(n);if(r.length===0)return this._logger.debug(`No rules found in directory: ${n}`),{successfulImports:0,duplicates:0,totalAttempted:0};this._logger.debug(`Loaded ${r.length} existing rules from ${n}`);let a=0,o=0;const i=r.length;for(const c of r)try{const l=(void 0)(c.path).name,d=(void 0)(c.path),u=d==="."?l:(void 0)(d,l);await this.createRule({path:u,content:c.content,type:c.type},s),a++,this._logger.debug(`Successfully imported rule: ${c.path} -> ${u}`)}catch(l){this._logger.warn(`Failed to import rule ${c.path}: ${String(l)}`),String(l).includes("already exists")&&o++}return this._logger.info(`Imported ${a} rules from ${n}, ${o} duplicates skipped`),{successfulImports:a,duplicates:o,totalAttempted:i}}catch(r){return this._logger.error(`Error importing directory: ${String(r)}`),{successfulImports:0,duplicates:0,totalAttempted:0}}}async detectAutoImportOptions(){const n=qe();if(!n)return this._logger.warn("No workspace available for auto-import detection"),[];const s=await n.getWorkspaceRoot();if(!s)return this._logger.warn("No workspace root found for auto-import detection"),[];const r=[];for(const{directory:a,file:o,name:i}of Io){let c=!1,l=!1;if(a)try{const d=(void 0)(s,a),u=await n.getPathInfo(d);c=u.exists===!0&&u.type===Ie.Directory}catch(d){this._logger.debug(`Error checking directory ${a}: ${String(d)}`)}if(o)try{const d=(void 0)(s,o),u=await n.getPathInfo(d);l=u.exists===!0&&u.type===Ie.File}catch(d){this._logger.debug(`Error checking file ${o}: ${String(d)}`)}c&&l?r.push({label:i,description:`Import existing rules from ${a} and ${o}`,directory:a,file:o}):c?r.push({label:i,description:`Import existing rules from ${a}`,directory:a}):l&&r.push({label:i,description:`Import existing rules from ${o}`,file:o})}return r}async processAutoImportSelection(n){const s=Io.find(l=>l.name===n);if(!s)throw new Error(`Unknown auto-import option: ${n}`);const r=[];s.directory&&r.push(this.importDirectory(s.directory,!0)),s.file&&r.push(this.importFile(s.file,!0));const a=await Promise.all(r),o=a.reduce((l,d)=>l+d.successfulImports,0),i=a.reduce((l,d)=>l+d.duplicates,0),c=a.reduce((l,d)=>l+d.totalAttempted,0);return this._logger.debug(`Auto-import rules completed for ${n}, imported: ${o}, duplicates: ${i}, total attempted: ${c}`),{importedRulesCount:o,duplicatesCount:i,totalAttempted:c,source:n}}autoImportRules(){this._logger.debug("Auto import rules requested")}dispose(){super.dispose()}}for(var ks=256,fh=[];ks--;)fh[ks]=(ks+256).toString(16).substring(1);const yh={THREAD_CREATION_ATTEMPTED:"thread_creation_attempted",SEND_ACTION_TRIGGERED:"send_action_triggered",CANCEL_ACTION_TRIGGERED:"cancel_action_triggered",RESEND_ACTION_TRIGGERED:"resend_action_triggered",AGENT_EXECUTION_MODE_TOGGLED:"agent_execution_mode_toggled",MESSAGE_SEND_RETRY_CLICKED:"message_send_retry_clicked",MESSAGE_SEND_TIMING:"message_send_timing"},Cs=new Map,_h=()=>{let e=Promise.resolve();const t=new Map,n=new Map,s=crypto.randomUUID(),r={end:a=>{const o=t.get(a);return console.debug("END LINK: ",a,s),o==null||o(),r},start:async(a,o)=>{const{promise:i,unlock:c,reject:l}=(u=>{let h=()=>{},p=()=>{},m=(y,f)=>()=>{f&&clearTimeout(f),y()};return{promise:new Promise((y,f)=>{let b,S=()=>{f("Chain was reset")};u&&u>0&&(b=setTimeout(S,u)),p=m(S,b),h=m(y,b)}),unlock:h,reject:p}})(o),d=e;return e=i.finally(()=>{t.delete(a),n.delete(a)}),t.set(a,()=>{c(),t.delete(a)}),n.set(a,()=>{l(),n.delete(a)}),await d,console.debug("START LINK: ",a,s),r},rejectAll:()=>{e=Promise.resolve();try{n.forEach(a=>{a(new Error("Chain was reset"))})}finally{t.clear(),n.clear()}},unlockAll:()=>{e=Promise.resolve();try{t.forEach(a=>{a()})}finally{t.clear(),n.clear()}}};return r},En="temp-fe";class le{constructor(t,n,s,r,a,o){g(this,"_state");g(this,"_subscribers",new Set);g(this,"_focusModel",new Bu);g(this,"_onSendExchangeListeners",[]);g(this,"_onNewConversationListeners",[]);g(this,"_onHistoryDeleteListeners",[]);g(this,"_onBeforeChangeConversationListeners",[]);g(this,"_eventTracker");g(this,"_totalCharactersCacheThrottleMs",1e3);g(this,"_totalCharactersStore");g(this,"_chatHistorySummarizationModel");g(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));g(this,"setConversation",(t,n=!0,s=!0)=>{const r=t.id!==this._state.id;r&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,i])=>{if(i.requestId&&i.toolUseId){const{requestId:c,toolUseId:l}=Ba(o);return c===i.requestId&&l===i.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",ls(i)),[o,i]}return[o,{...i,...Ba(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),n&&r&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const a=le.isEmpty(t);if(r&&a){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(H)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),r&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});g(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});g(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});g(this,"setName",t=>{this.update({name:t})});g(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});g(this,"updateFeedback",(t,n)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:n}})});g(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[ls(t)]:t}})});g(this,"getToolUseState",(t,n)=>t===void 0||n===void 0||this.toolUseStates===void 0?{phase:M.unknown,requestId:t??"",toolUseId:n??""}:this.toolUseStates[ls({requestId:t,toolUseId:n})]||{phase:M.new});g(this,"getLastToolUseId",()=>{var s,r;const t=this.lastExchange;if(!t)return;const n=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(a=>a.type===R.TOOL_USE))??[]).at(-1);return n?(r=n.tool_use)==null?void 0:r.tool_use_id:void 0});g(this,"getLastToolUseState",()=>{var s;const t=this.lastExchange;if(!t)return{phase:M.unknown};const n=function(r=[]){let a;for(const o of r){if(o.type===R.TOOL_USE)return o;o.type===R.TOOL_USE_START&&(a=o)}return a}(t==null?void 0:t.structured_output_nodes);return n?this.getToolUseState(t.request_id,(s=n.tool_use)==null?void 0:s.tool_use_id):{phase:M.unknown}});g(this,"addExchange",(t,n)=>{const s=this._state.chatHistory;let r,a;r=n===void 0?[...s,t]:n===-1?s.length===0?[t]:[...s.slice(0,-1),t,s[s.length-1]]:[...s.slice(0,n),t,...s.slice(n)],H(t)&&(a=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Pi.unset,feedbackNote:""}}:void 0),this.update({chatHistory:r,...a?{feedbackStates:a}:{},lastUrl:void 0})});g(this,"addExchangeBeforeLast",t=>{this.addExchange(t,-1)});g(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});g(this,"updateExchangeById",(t,n,s=!1)=>{var i;const r=this.exchangeWithRequestId(n);if(r===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(r.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=function(c=[]){const l=Nc(c);return l&&l.type===R.TOOL_USE?c.filter(d=>d.type!==R.TOOL_USE_START):c}([...r.structured_output_nodes??[],...t.structured_output_nodes??[]])),t.stop_reason!==r.stop_reason&&r.stop_reason&&t.stop_reason===yl.REASON_UNSPECIFIED&&(t.stop_reason=r.stop_reason),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...r.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const a=(i=(t.structured_output_nodes||[]).find(c=>c.type===R.MAIN_TEXT_FINISHED))==null?void 0:i.content;a&&a!==t.response_text&&(t.response_text=a);let o=this._state.isShareable||xs({...r,...t});return this.update({chatHistory:this.chatHistory.map(c=>c.request_id===n?{...c,...t}:c),isShareable:o}),!0});g(this,"clearMessagesFromHistory",t=>{const n=this._collectToolUseIdsFromMessages(this.chatHistory.filter(s=>s.request_id&&t.has(s.request_id)));this.update({chatHistory:this.chatHistory.filter(s=>!s.request_id||!t.has(s.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t),toolUseIds:n})});g(this,"clearHistory",()=>{const t=this._collectToolUseIdsFromMessages(this.chatHistory);this._extensionClient.clearMetadataFor({requestIds:this.requestIds,toolUseIds:t}),this.update({chatHistory:[]})});g(this,"clearHistoryFrom",async(t,n=!0)=>{const s=this.historyFrom(t,n),r=s.map(o=>o.request_id).filter(o=>o!==void 0),a=this._collectToolUseIdsFromMessages(s);this.update({chatHistory:this.historyTo(t,!n)}),this._extensionClient.clearMetadataFor({requestIds:r,toolUseIds:a}),s.forEach(o=>{this._onHistoryDeleteListeners.forEach(i=>i(o))})});g(this,"clearMessageFromHistory",t=>{const n=this.chatHistory.find(r=>r.request_id===t),s=n?this._collectToolUseIdsFromMessages([n]):[];this.update({chatHistory:this.chatHistory.filter(r=>r.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t],toolUseIds:s})});g(this,"_collectToolUseIdsFromMessages",t=>{var s;const n=[];for(const r of t)if(H(r)&&r.structured_output_nodes)for(const a of r.structured_output_nodes)a.type===R.TOOL_USE&&((s=a.tool_use)!=null&&s.tool_use_id)&&n.push(a.tool_use.tool_use_id);return n});g(this,"historyTo",(t,n=!1)=>{const s=this.chatHistory.findIndex(r=>r.request_id===t);return s===-1?[]:this.chatHistory.slice(0,n?s+1:s)});g(this,"historyFrom",(t,n=!0)=>{const s=this.chatHistory.findIndex(r=>r.request_id===t);return s===-1?[]:this.chatHistory.slice(n?s:s+1)});g(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});g(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:C.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id},!1,t.request_id)));g(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(n=>n!==t&&(!t.request_id||n.request_id!==t.request_id))})});g(this,"exchangeWithRequestId",t=>this.chatHistory.find(n=>n.request_id===t)||null);g(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});g(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const n={seen_state:te.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...n}:s)})});g(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));g(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const n=t.filter(s=>!s.personality&&!s.task);this.update({draftExchange:{...this.draftExchange,mentioned_items:n}})});g(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(n=>n.id);this.update({draftActiveContextIds:t})});g(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),n=this._specialContextInputModel.recentItems.filter(r=>t.has(r.id)||r.recentFile||r.selection||r.sourceFolder),s=this._specialContextInputModel.recentItems.filter(r=>!(t.has(r.id)||r.recentFile||r.selection||r.sourceFolder));this._specialContextInputModel.markItemsActive(n.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});g(this,"saveDraftExchange",(t,n)=>{var o,i,c;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),r=n!==((i=this.draftExchange)==null?void 0:i.rich_text_json_repr);if(!s&&!r)return;const a=(c=this.draftExchange)==null?void 0:c.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:n,mentioned_items:a,status:C.draft}})});g(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});g(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const n=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:n,model_id:this.selectedModelId??void 0}).then(()=>{var s;if(!Tt(this)){const r=!this.name&&this.chatHistory.length===1&&((s=this.firstExchange)==null?void 0:s.request_id)===this.chatHistory[0].request_id;this._chatFlagModel.summaryTitles&&r&&this.updateConversationTitle()}}).finally(()=>{var s;Tt(this)&&this._extensionClient.reportAgentRequestEvent({eventName:xn.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});g(this,"cancelMessage",async()=>{var n;if(!this.canCancelMessage||!((n=this.lastExchange)!=null&&n.request_id))return;const t=this.lastExchange.request_id;this.updateExchangeById({status:C.cancelled},t),await this._extensionClient.cancelChatStream(t)});g(this,"sendInstructionExchange",async(t,n)=>{let s=`${En}-${crypto.randomUUID()}`;const r={status:C.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:te.unseen,timestamp:new Date().toISOString()};this.addExchange(r);for await(const a of this._extensionClient.sendInstructionMessage(r,n)){if(!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});g(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});g(this,"checkAndGenerateAgentTitle",()=>{var n;if(!(!Tt(this)||!this._chatFlagModel.summaryTitles||this.name)){var t;!this.name&&(t=this.chatHistory,t.filter(s=>qs(s))).length===1&&!((n=this.extraData)!=null&&n.hasTitleGenerated)&&(this.update({extraData:{...this.extraData,hasTitleGenerated:!0}}),this.updateConversationTitle())}});g(this,"sendSummaryExchange",()=>{const t={status:C.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:ht.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});g(this,"generateCommitMessage",async()=>{let t=`${En}-${crypto.randomUUID()}`;const n={status:C.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:te.unseen,chatItemType:ht.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(n);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});g(this,"sendExchange",async(t,n=!1,s)=>{var u,h,p;this._chatHistorySummarizationModel.cancelRunningOrScheduledSummarizations(),this.updateLastInteraction();let r=`${En}-${crypto.randomUUID()}`,a=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(le.isNew(this._state)){const m=crypto.randomUUID(),y=this._state.id;try{await this._extensionClient.migrateConversationId(y,m)}catch(f){console.error("Failed to migrate conversation checkpoints:",f)}this._state={...this._state,id:m},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(m),this._subscribers.forEach(f=>f(this))}t=ko(t);let o={status:C.sent,request_id:r,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:a,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:te.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(o),this._loadContextFromExchange(o),this._onSendExchangeListeners.forEach(m=>m(o)),this._chatFlagModel.useHistorySummary&&!t.request_message&&await this._chatHistorySummarizationModel.maybeAddHistorySummaryNode()&&this.update({chatHistory:this._chatHistorySummarizationModel.clearStaleHistorySummaryNodes(this.chatHistory)}),o=await this._addIdeStateNode(o),this.updateExchangeById({structured_request_nodes:o.structured_request_nodes},r,!1);const i=Date.now();let c=!1,l=0;for await(const m of this.sendUserMessage(r,o,n,s)){if(((u=this.exchangeWithRequestId(r))==null?void 0:u.status)!==C.sent||!this.updateExchangeById(m,r,!0))return;if(r=m.request_id||r,!c&&Tt(this)){const y=Date.now();l=y-i,this._extensionClient.reportAgentRequestEvent({eventName:xn.firstTokenReceived,conversationId:this.id,requestId:r,chatHistoryLength:this.chatHistory.length,eventData:{firstTokenTimingData:{userMessageSentTimestampMs:i,firstTokenReceivedTimestampMs:y,timeToFirstTokenMs:l}}}),c=!0}}const d=Date.now()-i;(p=this._eventTracker)==null||p.trackEvent(yh.MESSAGE_SEND_TIMING,{requestId:r,timeToFirstTokenMs:l,timeToLastTokenMs:d,responseLength:(h=t==null?void 0:t.response_text)==null?void 0:h.length,chatHistoryLength:this.chatHistory.length,modelId:o.model_id}),this._chatHistorySummarizationModel.maybeScheduleSummarization(d)});g(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:C.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Ai.chatUseSuggestedQuestion)});g(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});g(this,"recoverExchange",async t=>{var r;if(!t.request_id||t.status!==C.sent)return;let n=t.request_id;const s=(r=t.structured_output_nodes)==null?void 0:r.filter(a=>a.type===R.AGENT_MEMORY);this.updateExchangeById({...t,response_text:t.lastChunkId?t.response_text:"",structured_output_nodes:t.lastChunkId?t.structured_output_nodes??[]:s},n);for await(const a of this.getChatStream(t)){if(!this.updateExchangeById(a,n,!0))return;n=a.request_id||n}});g(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(n=>{H(n)&&this._loadContextFromExchange(n)})});g(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});g(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(n=>{H(n)&&this._unloadContextFromExchange(n)})});g(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});g(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});g(this,"_jsonToStructuredRequest",t=>{const n=[],s=a=>{var i;const o=n.at(-1);if((o==null?void 0:o.type)===K.TEXT){const c=((i=o.text_node)==null?void 0:i.content)??"",l={...o,text_node:{content:c+a}};n[n.length-1]=l}else n.push({id:n.length,type:K.TEXT,text_node:{content:a}})},r=a=>{var o,i,c,l,d;if(a.type==="doc"||a.type==="paragraph")for(const u of a.content??[])r(u);else if(a.type==="hardBreak")s(`
`);else if(a.type==="text")s(a.text??"");else if(a.type==="file"){if(typeof((o=a.attrs)==null?void 0:o.src)!="string")return void console.error("File source is not a string: ",(i=a.attrs)==null?void 0:i.src);if(a.attrs.isLoading)return;const u=(c=a.attrs)==null?void 0:c.title,h=bl(u);vl(u)?n.push({id:n.length,type:K.IMAGE_ID,image_id_node:{image_id:a.attrs.src,format:h}}):n.push({id:n.length,type:K.FILE_ID,file_id_node:{file_id:a.attrs.src,file_name:u}})}else if(a.type==="mention"){const u=(l=a.attrs)==null?void 0:l.data;u&&Di(u)?n.push({id:n.length,type:K.TEXT,text_node:{content:Ac(this._chatFlagModel,u.personality.type)}}):u&&yc(u)?n.push({id:n.length,type:K.TEXT,text_node:{content:pd.getTaskOrchestratorPrompt(u.task)}}):s(`@\`${(u==null?void 0:u.name)??(u==null?void 0:u.id)}\``)}else if(a.type==="askMode"){const u=(d=a.attrs)==null?void 0:d.prompt;u&&n.push({id:n.length,type:K.TEXT,text_node:{content:u}})}};return r(t),n});this._extensionClient=t,this._chatFlagModel=n,this._specialContextInputModel=s,this._saveConversation=r,this._rulesModel=a,this._state={...le.create(o!=null&&o.forceAgentConversation?{extraData:{isAgentConversation:!0,hasAgentOnboarded:!0}}:void 0)},this._totalCharactersStore=this._createTotalCharactersStore(),this._chatHistorySummarizationModel=new ph(this,t,n)}get conversationId(){return this._state.id}insertChatItem(t,n){const s=[...this._state.chatHistory];s.splice(t,0,n),this.update({chatHistory:s})}_createTotalCharactersStore(){return lc(()=>{let t=0;const n=this._state.chatHistory;return this.convertHistoryToExchanges(n).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}setEventTracker(t){this._eventTracker=t}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,r)=>s+r,0))||0)<=4?z.PROTOTYPER:z.DEFAULT}catch(n){return console.error("Error determining persona type:",n),z.DEFAULT}}static create(t={}){const n=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:n,lastInteractedAtIso:n,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:z.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){if(t.name)return t.name;const n=t.chatHistory.find(H);return n&&n.request_message?le.toSentenceCase(n.request_message):Tt(t)?"New Agent":"New Chat"}static isNew(t){return t.id===Cc}static isEmpty(t){var r;const n=t.chatHistory.filter(a=>H(a)),s=t.chatHistory.filter(a=>vh(a));return n.length===0&&s.length===0&&!((r=t.draftExchange)!=null&&r.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,n){return n==="lastMessageTimestamp"?le.lastMessageTimestamp(t):n==="lastInteractedAt"?le.lastInteractedAt(t):le.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){var s;const n=(s=t.chatHistory.findLast(H))==null?void 0:s.timestamp;return n?new Date(n):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!le.isEmpty(t)||le.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(n=>n!==t)}}_notifyBeforeChangeConversation(t,n){let s=n;for(const r of this._onBeforeChangeConversationListeners){const a=r(t,s);a!==void 0&&(s=a)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return le.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??z.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return le.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return le.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}get flags(){return this._chatFlagModel}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",n=this.hasImagesInDraft();return t||n}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const n=r=>Array.isArray(r)?r.some(n):!!r&&(r.type==="file"||!(!r.content||!Array.isArray(r.content))&&r.content.some(n));return n(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){return this.chatHistory.find(H)??null}get lastExchange(){return this.chatHistory.findLast(H)??null}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>H(t)&&t.status===C.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>xs(t)||$t(t)||pt(t))}get totalCharactersStore(){return this._totalCharactersStore}convertHistoryToExchanges(t){if(t.length===0)return[];t=this._chatHistorySummarizationModel.preprocessChatHistory(t);const n=[];for(const s of t)if(xs(s))n.push(No(s));else if(pt(s))n.push(No(s));else if($t(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const r=bh(s,1),a={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[r],response_nodes:[]};n.push(a)}return n}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===C.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const n=crypto.randomUUID();let s,r="";const a=await this._addIdeStateNode(ko({...t,request_id:n,status:C.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(n,a,!0))o.response_text&&(r+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:r,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t.request_id,t.lastChunkId,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,n,s){return[]}_resolveUnresolvedToolUses(t,n,s){var d,u,h;if(t.length===0)return[t,n];const r=t[t.length-1],a=((d=r.response_nodes)==null?void 0:d.filter(p=>p.type===R.TOOL_USE))??[];if(a.length===0)return[t,n];const o=new Set;(u=n.structured_request_nodes)==null||u.forEach(p=>{var m;p.type===K.TOOL_RESULT&&((m=p.tool_result_node)!=null&&m.tool_use_id)&&o.add(p.tool_result_node.tool_use_id)});const i=a.filter(p=>{var y;const m=(y=p.tool_use)==null?void 0:y.tool_use_id;return m&&!o.has(m)});if(i.length===0)return[t,n];const c=i.map((p,m)=>{const y=p.tool_use.tool_use_id;return function(f,b,S,v){const T=Ic(b,f,v);let w;if(T!==void 0)w=T;else{let I;switch(b.phase){case M.runnable:I="Tool was cancelled before running.";break;case M.new:I="Cancelled by user.";break;case M.checkingSafety:I="Tool was cancelled during safety check.";break;case M.running:I="Tool was cancelled while running.";break;case M.cancelling:I="Tool cancellation was interrupted.";break;case M.cancelled:I="Cancelled by user.";break;case M.error:I="Tool execution failed.";break;case M.completed:I="Tool completed but result was unavailable.";break;case M.unknown:default:I="Cancelled by user.",b.phase!==M.unknown&&console.error(`Unexpected tool state phase: ${b.phase}`)}w={tool_use_id:f,content:I,is_error:!0}}return{id:S,type:K.TOOL_RESULT,tool_result_node:w}}(y,this.getToolUseState(r.request_id,y),fr(n.structured_request_nodes??[])+m+1,this._chatFlagModel.enableDebugFeatures)});if((h=n.structured_request_nodes)==null?void 0:h.some(p=>p.type===K.TOOL_RESULT))return[t,{...n,structured_request_nodes:[...n.structured_request_nodes??[],...c]}];{const p={request_message:"",response_text:"OK.",request_id:crypto.randomUUID(),structured_request_nodes:c,structured_output_nodes:[],status:C.success,hidden:!0};return s||this.addExchangeBeforeLast(p),[t.concat(this.convertHistoryToExchanges([p])),n]}}async*sendUserMessage(t,n,s,r){const a=this._chatFlagModel.enableParallelTools,o=await(a?((i,c,l)=>{const d=Cs.get(i)??_h();return Cs.has(i)||Cs.set(i,d),d.start(c,l)})("sendMessage",t):Promise.resolve({end:()=>{}}));try{for await(const i of this._sendUserMessage(t,n,s,r))yield i}finally{o.end(t)}}async*_sendUserMessage(t,n,s,r){var h;const a=this._specialContextInputModel.chatActiveContext;let o;if(n.chatHistory!==void 0)o=n.chatHistory;else{let p=this.successfulMessages;if(n.chatItemType===ht.summaryTitle){const m=p.findIndex(y=>y.chatItemType!==ht.agentOnboarding&&qs(y));m!==-1&&(p=p.slice(m))}o=this.convertHistoryToExchanges(p)}this._chatFlagModel.enableParallelTools&&([o,n]=this._resolveUnresolvedToolUses(o,n,s));let i=this.personaType;if(n.structured_request_nodes){const p=n.structured_request_nodes.find(m=>m.type===K.CHANGE_PERSONALITY);p&&p.change_personality_node&&(i=p.change_personality_node.personality_type)}let c=[];if(this._chatFlagModel.enableRules&&this._rulesModel){this._rulesModel.requestRules();const p=Sl(this._rulesModel.getCachedRules());c=Br.filterRulesByContext(p,a.ruleFiles||[])}const l={text:n.request_message,chatHistory:o,silent:s,modelId:n.model_id,context:a,userSpecifiedFiles:a.userSpecifiedFiles,externalSourceIds:(h=a.externalSources)==null?void 0:h.map(p=>p.id),disableRetrieval:n.disableRetrieval??!1,disableSelectedCodeDetails:n.disableSelectedCodeDetails??!1,nodes:n.structured_request_nodes,memoriesInfo:n.memoriesInfo,personaType:i,conversationId:this.id,createdTimestamp:Date.now(),requestIdOverride:r,rules:c},d=this._createStreamStateHandlers(t,l,{flags:this._chatFlagModel}),u=this._extensionClient.startChatStreamWithRetry(t,l,{flags:this._chatFlagModel});for await(const p of u){let m=p;t=p.request_id||t;for(const y of d)m=y.handleChunk(m)??m;yield m}for(const p of d)yield*p.handleComplete();this.updateExchangeById({structured_request_nodes:n.structured_request_nodes},t)}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(n=>n!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(n=>n!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(n=>n!==t)}}updateChatItem(t,n){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...n}:s)}),!0)}async _addIdeStateNode(t){let n,s=(t.structured_request_nodes??[]).filter(r=>r.type!==K.IDE_STATE);try{n=await this._extensionClient.getChatRequestIdeState()}catch(r){console.error("Failed to add IDE state to exchange:",r)}return n?(s=[...s,{id:fr(s)+1,type:K.IDE_STATE,ide_state_node:n}],{...t,structured_request_nodes:s}):t}}function bh(e,t){const n=($t(e),e.fromTimestamp),s=($t(e),e.toTimestamp),r=$t(e)&&e.revertTarget!==void 0;return{id:t,type:K.CHECKPOINT_REF,checkpoint_ref_node:{request_id:e.request_id||"",from_timestamp:n,to_timestamp:s,source:r?fl.CHECKPOINT_REVERT:void 0}}}function No(e){const t=(e.structured_output_nodes??[]).filter(n=>n.type===R.RAW_RESPONSE||n.type===R.TOOL_USE||n.type===R.TOOL_USE_START).map(n=>n.type===R.TOOL_USE_START?{...n,tool_use:{...n.tool_use,input_json:"{}"},type:R.TOOL_USE}:n);return{request_message:e.request_message,response_text:e.response_text??"",request_id:e.request_id||"",request_nodes:e.structured_request_nodes??[],response_nodes:t}}function fr(e){return e.length>0?Math.max(...e.map(t=>t.id)):0}function ko(e){var t;if(e.request_message.length>0&&!((t=e.structured_request_nodes)!=null&&t.some(n=>n.type===K.TEXT))){let n=e.structured_request_nodes??[];return n=[...n,{id:fr(n)+1,type:K.TEXT,text_node:{content:e.request_message}}],{...e,structured_request_nodes:n}}return e}const Yp="augment-welcome";var C=(e=>(e.draft="draft",e.sent="sent",e.failed="failed",e.success="success",e.cancelled="cancelled",e))(C||{}),Qe=(e=>(e.running="running",e.awaitingUserAction="awaiting-user-action",e.notRunning="not-running",e))(Qe||{}),te=(e=>(e.seen="seen",e.unseen="unseen",e))(te||{}),ht=(e=>(e.signInWelcome="sign-in-welcome",e.generateCommitMessage="generate-commit-message",e.summaryResponse="summary-response",e.summaryTitle="summary-title",e.educateFeatures="educate-features",e.agentOnboarding="agent-onboarding",e.agenticTurnDelimiter="agentic-turn-delimiter",e.agenticRevertDelimiter="agentic-revert-delimiter",e.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",e.exchange="exchange",e.exchangePointer="exchange-pointer",e.historySummary="history-summary",e))(ht||{});function Co(e){return H(e)||Sh(e)||Eh(e)}function H(e){return!!e&&(e.chatItemType===void 0||e.chatItemType==="agent-onboarding")}function xs(e){return H(e)&&e.status==="success"}function vh(e){return!!e&&e.chatItemType==="exchange-pointer"}function Kp(e){return e.chatItemType==="sign-in-welcome"}function Sh(e){return e.chatItemType==="generate-commit-message"}function Wp(e){return e.chatItemType==="summary-response"}function zp(e){return e.chatItemType==="educate-features"}function Eh(e){return e.chatItemType==="agent-onboarding"}function Xp(e){return e.chatItemType==="agentic-turn-delimiter"}function $t(e){return e.chatItemType==="agentic-checkpoint-delimiter"}function pt(e){return e.chatItemType==="history-summary"}function Jp(e){return e.revertTarget!==void 0}function Zp(e,t){const n=function(r){if(!r)return;const a=r.findLast(o=>Co(o.turn));return a?a.turn:void 0}(e);if(!((n==null?void 0:n.status)==="success"||(n==null?void 0:n.status)==="failed"||(n==null?void 0:n.status)==="cancelled"))return!1;const s=function(r){return r?r.findLast(o=>{var i;return!((i=o.turn.request_id)!=null&&i.startsWith(En))&&Co(o.turn)}):void 0}(e);return(s==null?void 0:s.turn.request_id)===t.request_id}function Qp(e){var t;return((t=e.structured_output_nodes)==null?void 0:t.some(n=>n.type===R.TOOL_USE))??!1}function Th(e){var t;return((t=e.structured_request_nodes)==null?void 0:t.some(n=>n.type===K.TOOL_RESULT))??!1}function em(e){return!(!e||typeof e!="object")&&(!("request_id"in e)||typeof e.request_id=="string")&&(!("seen_state"in e)||e.seen_state==="seen"||e.seen_state==="unseen")}function tm(e){return(e==null?void 0:e.status)==="success"||(e==null?void 0:e.status)==="failed"||(e==null?void 0:e.status)==="cancelled"}function nm(e){if(!e)return;const t=e.filter(n=>H(n.turn)).map(n=>{return"response_text"in(s=n.turn)?s.response_text??"":"";var s}).filter(n=>n.length>0);return t.length>0?t.join(`
`):void 0}function sm(e){let t=[];if(!e)return t;for(const n of e){const s=n.turn;H(s)&&s.structured_output_nodes&&(t=t.concat(s.structured_output_nodes))}return t}function rm(e){var n;let t=new Set;for(const s of e)if(s.type===R.TOOL_USE&&((n=s.tool_use)!=null&&n.input_json))try{const r=JSON.parse(s.tool_use.input_json).path;t.add(r)}catch(r){console.error("Failed to parse tool input JSON:",r)}return t.size}function am(e){var t;return e.type===R.AGENT_MEMORY||e.type===R.TOOL_USE&&((t=e.tool_use)==null?void 0:t.tool_name)==="remember"}function om(e){var t;return e.type===R.TOOL_USE&&((t=e.tool_use)==null?void 0:t.tool_name)==="view"}function im(e){var t;return e.type===R.TOOL_USE&&((t=e.tool_use)==null?void 0:t.tool_name)==="str-replace-editor"}async function*wh(e,t=1e3){for(;e>0;)yield e,await new Promise(n=>setTimeout(n,Math.min(t,e))),e-=t}class Ih{constructor(t,n,s,r=5,a=4e3,o){g(this,"_isCancelled",!1);this.requestId=t,this.chatMessage=n,this.startStreamFn=s,this.maxRetries=r,this.baseDelay=a,this.flags=o}cancel(){this._isCancelled=!0}async*getStream(){let t=0,n=0,s=!1;try{for(;!this._isCancelled;){const r=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let a,o,i=!1,c=!0;for await(const l of r){if(l.status===C.failed){if(l.isRetriable!==!0||s)return yield l;i=!0,c=l.shouldBackoff??!0,a=l.display_error_message,o=l.request_id;break}s=!0,yield l}if(!i)return;if(this._isCancelled)return yield this.createCancelledStatus();if(t++,t>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${a}`),void(yield{request_id:o??this.requestId,seen_state:te.unseen,status:C.failed,display_error_message:a,isRetriable:!1});if(c){const l=this.baseDelay*2**n;n++;for await(const d of wh(l))yield{request_id:this.requestId,status:C.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(d/1e3)} seconds... (Attempt ${t} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:C.sent,display_error_message:`Generating response... (Attempt ${t+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(r){console.error("Unexpected error in chat stream:",r),yield{request_id:this.requestId,seen_state:te.unseen,status:C.failed,display_error_message:r instanceof Error?r.message:String(r)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:te.unseen,status:C.cancelled}}}var ct=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(ct||{});class Nh{constructor(t){g(this,"getHydratedTask",async t=>{const n={type:ct.getHydratedTaskRequest,data:{uuid:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.task});g(this,"createTask",async(t,n,s)=>{const r={type:ct.createTaskRequest,data:{name:t,description:n,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(r,3e4)).data.uuid});g(this,"updateTask",async(t,n,s)=>{const r={type:ct.updateTaskRequest,data:{uuid:t,updates:n,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(r,3e4)});g(this,"setCurrentRootTaskUuid",t=>{const n={type:ct.setCurrentRootTaskUuid,data:{uuid:t}};this._asyncMsgSender.sendToSidecar(n)});g(this,"updateHydratedTask",async(t,n)=>{const s={type:ct.updateHydratedTaskRequest,data:{task:t,updatedBy:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=t}}var Ne=(e=>(e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.openGuidelines="open-guidelines",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.updateRuleFileResponse="update-rule-file-response",e.getWorkspaceRoot="get-workspace-root",e.getWorkspaceRootResponse="get-workspace-root-response",e.autoImportRules="auto-import-rules",e.autoImportRulesOptionsResponse="auto-import-rules-options-response",e.autoImportRulesSelectionRequest="auto-import-rules-selection-request",e.autoImportRulesResponse="auto-import-rules-response",e.processSelectedPathsRequest="process-selected-paths-request",e.processSelectedPathsResponse="process-selected-paths-response",e))(Ne||{}),Tn=(e=>(e.loadConversationToolUseStatesRequest="load-conversation-tooluse-states-request",e.loadConversationToolUseStatesResponse="load-conversation-tooluse-states-response",e.saveToolUseStatesRequest="save-tooluse-states-request",e.saveToolUseStatesResponse="save-tooluse-states-response",e.deleteConversationToolUseStatesRequest="delete-conversation-tooluse-states-request",e.deleteConversationToolUseStatesResponse="delete-conversation-tooluse-states-response",e))(Tn||{});class lm{constructor(t,n,s){g(this,"_taskClient");g(this,"getChatInitData",async()=>{const t=await this._asyncMsgSender.send({type:E.chatLoaded},3e4);if(t.data.enableDebugFeatures)try{console.log("Running hello world test...");const n=await async function(s){return(await $u(Hu,new ki({sendMessage:a=>{s.postMessage(a)},onReceiveMessage:a=>{const o=i=>{a(i.data)};return window.addEventListener("message",o),()=>{window.removeEventListener("message",o)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",n)}catch(n){console.error("Hello world error:",n)}return t.data});g(this,"reportWebviewClientEvent",t=>{this._asyncMsgSender.send({type:E.reportWebviewClientMetric,data:{webviewName:xi.chat,client_metric:t,value:1}})});g(this,"trackEventWithTypes",(t,n)=>{this._asyncMsgSender.send({type:E.trackAnalyticsEvent,data:{eventName:t,properties:n}})});g(this,"reportAgentSessionEvent",t=>{this._asyncMsgSender.sendToSidecar({type:j.reportAgentSessionEvent,data:t})});g(this,"reportAgentRequestEvent",t=>{this._asyncMsgSender.sendToSidecar({type:j.reportAgentRequestEvent,data:t})});g(this,"getSuggestions",async(t,n=!1)=>{const s={rootPath:"",relPath:t},r=this.findFiles(s,6),a=this.findRecentlyOpenedFiles(s,6),o=this.findFolders(s,3),i=this.findExternalSources(t,n),c=this._flags.enableRules?this.findRules(t,6):Promise.resolve([]),[l,d,u,h,p]=await Promise.all([kt(r,[]),kt(a,[]),kt(o,[]),kt(i,[]),kt(c,[])]),m=(f,b)=>({..._c(f),[b]:f}),y=[...l.map(f=>m(f,"file")),...u.map(f=>m(f,"folder")),...d.map(f=>m(f,"recentFile")),...h.map(f=>({label:f.name,name:f.name,id:f.id,externalSource:f})),...p.map(f=>({...bc(f),rule:f}))];if(this._flags.enablePersonalities){const f=this.getPersonalities(t);f.length>0&&y.push(...f)}return y});g(this,"getPersonalities",t=>{if(!this._flags.enablePersonalities)return[];if(t==="")return Ha;const n=t.toLowerCase();return Ha.filter(s=>{const r=s.personality.description.toLowerCase(),a=s.label.toLowerCase();return r.includes(n)||a.includes(n)})});g(this,"sendAction",t=>{this._host.postMessage({type:E.mainPanelPerformAction,data:t})});g(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:E.showAugmentPanel})});g(this,"showNotification",t=>{this._host.postMessage({type:E.showNotification,data:t})});g(this,"openConfirmationModal",async t=>(await this._asyncMsgSender.send({type:E.openConfirmationModal,data:t},1e9)).data.ok);g(this,"clearMetadataFor",t=>{this._host.postMessage({type:E.chatClearMetadata,data:t})});g(this,"resolvePath",async(t,n=void 0)=>{const s=await this._asyncMsgSender.send({type:E.resolveFileRequest,data:{...t,exactMatch:!0,maxResults:1,searchScope:n}},5e3);if(s.data)return s.data});g(this,"resolveSymbols",async(t,n)=>(await this._asyncMsgSender.send({type:E.findSymbolRequest,data:{query:t,searchScope:n}},3e4)).data);g(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:E.getDiagnosticsRequest},1e3)).data);g(this,"findFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:E.findFileRequest,data:{...t,maxResults:n}},5e3)).data);g(this,"findFolders",async(t,n=12)=>(await this._asyncMsgSender.send({type:E.findFolderRequest,data:{...t,maxResults:n}},5e3)).data);g(this,"findRecentlyOpenedFiles",async(t,n=12)=>(await this._asyncMsgSender.send({type:E.findRecentlyOpenedFilesRequest,data:{...t,maxResults:n}},5e3)).data);g(this,"findExternalSources",async(t,n=!1)=>this._flags.enableExternalSourcesInChat?n?[]:(await this._asyncMsgSender.send({type:E.findExternalSourcesRequest,data:{query:t,source_types:[]}},5e3)).data.sources??[]:[]);g(this,"findRules",async(t,n=12)=>(await this._asyncMsgSender.sendToSidecar({type:Ne.getRulesListRequest,data:{query:t,maxResults:n}})).data.rules);g(this,"openFile",t=>{this._host.postMessage({type:E.openFile,data:t})});g(this,"saveFile",t=>this._host.postMessage({type:E.saveFile,data:t}));g(this,"loadFile",t=>this._host.postMessage({type:E.loadFile,data:t}));g(this,"openMemoriesFile",()=>{this._host.postMessage({type:E.openMemoriesFile})});g(this,"canShowTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:E.canShowTerminal,data:{terminalId:t,command:n}},5e3)).data.canShow}catch(s){return console.error("Failed to check if terminal can be shown:",s),!1}});g(this,"showTerminal",async(t,n)=>{try{return(await this._asyncMsgSender.send({type:E.showTerminal,data:{terminalId:t,command:n}},5e3)).data.success}catch(s){return console.error("Failed to show terminal:",s),!1}});g(this,"createFile",(t,n)=>{this._host.postMessage({type:E.chatCreateFile,data:{code:t,relPath:n}})});g(this,"openScratchFile",async(t,n="shellscript")=>{await this._asyncMsgSender.send({type:E.openScratchFileRequest,data:{content:t,language:n}},1e4)});g(this,"resolveWorkspaceFileChunk",async t=>{try{return(await this._asyncMsgSender.send({type:E.resolveWorkspaceFileChunkRequest,data:t},5e3)).data}catch{return}});g(this,"smartPaste",t=>{this._host.postMessage({type:E.chatSmartPaste,data:t})});g(this,"getHydratedTask",async t=>this._taskClient.getHydratedTask(t));g(this,"updateHydratedTask",async(t,n)=>this._taskClient.updateHydratedTask(t,n));g(this,"setCurrentRootTaskUuid",t=>{this._taskClient.setCurrentRootTaskUuid(t)});g(this,"createTask",async(t,n,s)=>this._taskClient.createTask(t,n,s));g(this,"updateTask",async(t,n,s)=>this._taskClient.updateTask(t,n,s));g(this,"saveChat",async(t,n,s)=>this._asyncMsgSender.send({type:E.saveChat,data:{conversationId:t,chatHistory:n,title:s}},5e3));g(this,"updateUserGuidelines",t=>{this._host.postMessage({type:E.updateUserGuidelines,data:t})});g(this,"updateWorkspaceGuidelines",t=>{this._host.postMessage({type:E.updateWorkspaceGuidelines,data:t})});g(this,"openSettingsPage",t=>{this._host.postMessage({type:E.openSettingsPage,data:t})});g(this,"_activeRetryStreams",new Map);g(this,"cancelChatStream",async t=>{var n;(n=this._activeRetryStreams.get(t))==null||n.cancel(),await this._asyncMsgSender.send({type:E.chatUserCancel,data:{requestId:t}},1e4)});g(this,"sendUserRating",async(t,n,s,r="")=>{const a={requestId:t,rating:s,note:r,mode:n},o={type:E.chatRating,data:a};return(await this._asyncMsgSender.send(o,3e4)).data});g(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:E.usedChat})});g(this,"createProject",t=>{this._host.postMessage({type:E.mainPanelCreateProject,data:{name:t}})});g(this,"openProjectFolder",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"open-folder"})});g(this,"closeProjectFolder",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"close-folder"})});g(this,"cloneRepository",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"clone-repository"})});g(this,"grantSyncPermission",()=>{this._host.postMessage({type:E.mainPanelPerformAction,data:"grant-sync-permission"})});g(this,"startRemoteMCPAuth",t=>{this._host.postMessage({type:E.startRemoteMCPAuth,data:{name:t}})});g(this,"callTool",async(t,n,s,r,a,o)=>{const i={type:E.callTool,data:{chatRequestId:t,toolUseId:n,name:s,input:r,chatHistory:a,conversationId:o}};return(await this._asyncMsgSender.send(i,0)).data});g(this,"cancelToolRun",async(t,n)=>{const s={type:E.cancelToolRun,data:{requestId:t,toolUseId:n}};await this._asyncMsgSender.send(s,0)});g(this,"checkSafe",async t=>{const n={type:bn.checkToolCallSafeRequest,data:t};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});g(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:bn.closeAllToolProcesses},0)});g(this,"getToolIdentifier",async t=>{const n={type:bn.getToolIdentifierRequest,data:{toolName:t}};return(await this._asyncMsgSender.sendToSidecar(n,0)).data});g(this,"getChatMode",async()=>{const t={type:j.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.chatMode});g(this,"setChatMode",t=>{this._asyncMsgSender.send({type:E.chatModeChanged,data:{mode:t}})});g(this,"getAgentEditList",async(t,n)=>{const s={type:j.getEditListRequest,data:{fromTimestamp:t,toTimestamp:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});g(this,"hasChangesSince",async t=>{const n={type:j.getEditListRequest,data:{fromTimestamp:t,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.edits.filter(s=>{var r,a;return((r=s.changesSummary)==null?void 0:r.totalAddedLines)||((a=s.changesSummary)==null?void 0:a.totalRemovedLines)}).length>0});g(this,"getToolCallCheckpoint",async t=>{const n={type:E.getToolCallCheckpoint,data:{requestId:t}};return(await this._asyncMsgSender.send(n,3e4)).data.checkpointNumber});g(this,"setCurrentConversation",t=>{this._asyncMsgSender.sendToSidecar({type:j.setCurrentConversation,data:{conversationId:t}})});g(this,"migrateConversationId",async(t,n)=>{await this._asyncMsgSender.sendToSidecar({type:j.migrateConversationId,data:{oldConversationId:t,newConversationId:n}},3e4)});g(this,"showAgentReview",(t,n,s,r=!0,a)=>{this._asyncMsgSender.sendToSidecar({type:j.chatReviewAgentFile,data:{qualifiedPathName:t,fromTimestamp:n,toTimestamp:s,retainFocus:r,useNativeDiffIfAvailable:a}})});g(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:j.chatAgentEditAcceptAll}),!0));g(this,"revertToTimestamp",async(t,n)=>(await this._asyncMsgSender.sendToSidecar({type:j.revertToTimestamp,data:{timestamp:t,qualifiedPathNames:n}}),!0));g(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:E.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);g(this,"getAgentEditChangesByRequestId",async t=>{const n={type:j.getEditChangesByRequestIdRequest,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});g(this,"getAgentEditContentsByRequestId",async t=>{const n={type:j.getAgentEditContentsByRequestId,data:{requestId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});g(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:E.triggerInitialOrientation})});g(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:E.getWorkspaceInfoRequest},5e3)).data}catch(t){return console.error("Error getting workspace info:",t),{}}});g(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:E.toggleCollapseUnchangedRegions})});g(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:E.checkAgentAutoModeApproval},5e3)).data);g(this,"setAgentAutoModeApproved",async t=>{await this._asyncMsgSender.send({type:E.setAgentAutoModeApproved,data:t},5e3)});g(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:j.checkHasEverUsedAgent},5e3)).data);g(this,"setHasEverUsedAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:j.setHasEverUsedAgent,data:t},5e3)});g(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:j.checkHasEverUsedRemoteAgent},5e3)).data);g(this,"setHasEverUsedRemoteAgent",async t=>{await this._asyncMsgSender.sendToSidecar({type:j.setHasEverUsedRemoteAgent,data:t},5e3)});g(this,"getChatRequestIdeState",async()=>{const t={type:E.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(t,3e4)).data});g(this,"reportError",t=>{this._host.postMessage({type:E.reportError,data:t})});g(this,"sendMemoryCreated",async t=>{await this._asyncMsgSender.sendToSidecar(t,5e3)});g(this,"sendGitMessage",async t=>await this._asyncMsgSender.sendToSidecar(t,3e4));this._host=t,this._asyncMsgSender=n,this._flags=s,this._taskClient=new Nh(n)}async*generateCommitMessage(){const t={type:E.generateCommitMessage},n=this._asyncMsgSender.stream(t,3e4,6e4);yield*As(n,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(t,n){const s={instruction:t.request_message??"",selectedCodeDetails:n,requestId:t.request_id},r={type:E.chatInstructionMessage,data:s},a=this._asyncMsgSender.stream(r,3e4,6e4);yield*async function*(o){let i;try{for await(const c of o)i=c.data.requestId,yield{request_id:i,response_text:c.data.text,seen_state:te.unseen,status:C.sent};yield{request_id:i,seen_state:te.unseen,status:C.success}}catch(c){console.error("Error in chat instruction model reply stream:",c),yield{request_id:i,seen_state:te.unseen,status:C.failed}}}(a)}async openGuidelines(t){this._host.postMessage({type:E.openGuidelines,data:t})}async*getExistingChatStream(t,n,s){const r=s==null?void 0:s.flags.enablePreferenceCollection,a=r?1e9:6e4,o=r?1e9:3e5,i={type:E.chatGetStreamRequest,data:{requestId:t,lastChunkId:n}},c=this._asyncMsgSender.stream(i,a,o);yield*As(c,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(t,n){const s=n==null?void 0:n.flags.enablePreferenceCollection,r=s?1e9:1e5,a=s?1e9:3e5,o={type:E.chatUserMessage,data:t},i=this._asyncMsgSender.stream(o,r,a);yield*As(i,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(t){return(await this._asyncMsgSender.send({type:E.checkToolExists,toolName:t},0)).exists}async saveImage(t,n){const s=qa(await as(t)),r=n??`${await $a(await os(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:E.chatSaveImageRequest,data:{filename:r,data:s}},1e4)).data}async saveAttachment(t,n){const s=qa(await as(t)),r=n??`${await $a(await os(s))}.${t.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:E.chatSaveAttachmentRequest,data:{filename:r,data:s}},1e4)).data}async loadImage(t){const n=await this._asyncMsgSender.send({type:E.chatLoadImageRequest,data:t},1e4),s=n.data?await os(n.data):void 0;if(!s)return;let r="application/octet-stream";const a=t.split(".").at(-1);a==="png"?r="image/png":a!=="jpg"&&a!=="jpeg"||(r="image/jpeg");const o=new File([s],t,{type:r});return await as(o)}async deleteImage(t){await this._asyncMsgSender.send({type:E.chatDeleteImageRequest,data:t},1e4)}async*startChatStreamWithRetry(t,n,s){const r=new Ih(t,n,(a,o)=>this.startChatStream(a,o),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(t,r);try{yield*r.getStream()}finally{this._activeRetryStreams.delete(t)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:E.getSubscriptionInfo},5e3)}async loadExchanges(t,n){if(n.length===0)return[];const s={type:vn.loadExchangesByUuidsRequest,data:{conversationId:t,uuids:n}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.exchanges}async saveExchanges(t,n){if(n.length===0)return;const s={type:vn.saveExchangesRequest,data:{conversationId:t,exchanges:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationExchanges(t){const n={type:vn.deleteConversationExchangesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async loadConversationToolUseStates(t){const n={type:Tn.loadConversationToolUseStatesRequest,data:{conversationId:t}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.toolUseStates}async saveToolUseStates(t,n){if(Object.keys(n).length===0)return;const s={type:Tn.saveToolUseStatesRequest,data:{conversationId:t,toolUseStates:n}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async deleteConversationToolUseStates(t){const n={type:Tn.deleteConversationToolUseStatesRequest,data:{conversationId:t}};await this._asyncMsgSender.sendToSidecar(n,3e4)}}async function*As(e,t=()=>{},n){let s;try{for await(const r of e){if(s=r.data.requestId,r.data.error)return console.error("Error in chat model reply stream:",r.data.error.displayErrorMessage),yield{request_id:s,seen_state:te.unseen,status:C.failed,display_error_message:r.data.error.displayErrorMessage,isRetriable:r.data.error.isRetriable,shouldBackoff:r.data.error.shouldBackoff};const a={request_id:s,response_text:r.data.text,workspace_file_chunks:r.data.workspaceFileChunks,structured_output_nodes:kh(r.data.nodes),seen_state:te.unseen,status:C.sent,lastChunkId:r.data.chunkId};r.data.stop_reason!=null&&(a.stop_reason=r.data.stop_reason),yield a}yield{request_id:s,seen_state:te.unseen,status:C.success}}catch(r){let a,o;if(t({originalRequestId:s||"",sanitizedMessage:r instanceof Error?r.message:String(r),stackTrace:r instanceof Error&&r.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),r instanceof hl&&n)switch(r.name){case"MessageTimeout":a=!0,o=!1;break;case"StreamTimeout":case"InvalidResponse":a=!1}console.error("Unexpected error in chat model reply stream:",r),yield{request_id:s,seen_state:te.unseen,status:C.failed,isRetriable:a,shouldBackoff:o}}}async function kt(e,t){try{return await e}catch(n){return console.warn(`Error while resolving promise: ${n}`),t}}function kh(e){if(!e)return e;let t=!1;return e.filter(n=>n.type!==R.TOOL_USE||!t&&(t=!0,!0))}const um=15,cm=1e3,Ch=25e4,dm=2e4;class hm{constructor(t){g(this,"_enableEditableHistory",!1);g(this,"_enablePreferenceCollection",!1);g(this,"_enableRetrievalDataCollection",!1);g(this,"_enableDebugFeatures",!1);g(this,"_enableConversationDebugUtils",!1);g(this,"_enableRichTextHistory",!1);g(this,"_enableAgentSwarmMode",!1);g(this,"_modelDisplayNameToId",{});g(this,"_fullFeatured",!0);g(this,"_enableExternalSourcesInChat",!1);g(this,"_smallSyncThreshold",15);g(this,"_bigSyncThreshold",1e3);g(this,"_enableSmartPaste",!1);g(this,"_enableDirectApply",!1);g(this,"_summaryTitles",!1);g(this,"_suggestedEditsAvailable",!1);g(this,"_enableShareService",!1);g(this,"_maxTrackableFileCount",Ch);g(this,"_enableDesignSystemRichTextEditor",!1);g(this,"_enableSources",!1);g(this,"_enableChatMermaidDiagrams",!1);g(this,"_smartPastePrecomputeMode",pl.visibleHover);g(this,"_useNewThreadsMenu",!1);g(this,"_enableChatMermaidDiagramsMinVersion",!1);g(this,"_enablePromptEnhancer",!1);g(this,"_idleNewSessionNotificationTimeoutMs");g(this,"_idleNewSessionMessageTimeoutMs");g(this,"_enableChatMultimodal",!1);g(this,"_enableAgentMode",!1);g(this,"_enableAgentAutoMode",!1);g(this,"_enableRichCheckpointInfo",!1);g(this,"_agentMemoriesFilePathName");g(this,"_conversationHistorySizeThresholdBytes",44040192);g(this,"_userTier","unknown");g(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});g(this,"_truncateChatHistory",!1);g(this,"_enableBackgroundAgents",!1);g(this,"_enableNewThreadsList",!1);g(this,"_customPersonalityPrompts",{});g(this,"_enablePersonalities",!1);g(this,"_enableRules",!1);g(this,"_memoryClassificationOnFirstToken",!1);g(this,"_enableGenerateCommitMessage",!1);g(this,"_modelRegistry",{});g(this,"_agentChatModel","");g(this,"_enableModelRegistry",!1);g(this,"_enableTaskList",!1);g(this,"_clientAnnouncement","");g(this,"_useHistorySummary",!1);g(this,"_historySummaryParams","");g(this,"_enableExchangeStorage",!1);g(this,"_enableToolUseStateStorage",!1);g(this,"_retryChatStreamTimeouts",!1);g(this,"_enableCommitIndexing",!1);g(this,"_enableMemoryRetrieval",!1);g(this,"_enableAgentTabs",!1);g(this,"_isVscodeVersionOutdated",!1);g(this,"_vscodeMinVersion","");g(this,"_enableGroupedTools",!1);g(this,"_remoteAgentsResumeHintAvailableTtlDays",0);g(this,"_enableParallelTools",!1);g(this,"_enableAgentGitTracker",!1);g(this,"_memoriesParams",{});g(this,"_subscribers",new Set);g(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));g(this,"update",t=>{this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=t.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=t.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=t.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=t.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=t.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._agentChatModel=t.agentChatModel??this._agentChatModel,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=t.useHistorySummary??this._useHistorySummary,this._historySummaryParams=t.historySummaryParams??this._historySummaryParams,this._enableExchangeStorage=t.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=t.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=t.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=t.enableMemoryRetrieval??this._enableMemoryRetrieval,this._enableAgentTabs=t.enableAgentTabs??this._enableAgentTabs,this._isVscodeVersionOutdated=t.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=t.vscodeMinVersion??this._vscodeMinVersion,this._enableGroupedTools=t.enableGroupedTools??this._enableGroupedTools,this._remoteAgentsResumeHintAvailableTtlDays=t.remoteAgentsResumeHintAvailableTtlDays??this._remoteAgentsResumeHintAvailableTtlDays,this._enableToolUseStateStorage=t.enableToolUseStateStorage??this._enableToolUseStateStorage,this._enableParallelTools=t.enableParallelTools??this._enableParallelTools,this._enableAgentGitTracker=t.enableAgentGitTracker??this._enableAgentGitTracker,this._memoriesParams=t.memoriesParams??this._memoriesParams,this._subscribers.forEach(n=>n(this))});g(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")));g(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(n=>this._modelDisplayNameToId[n]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,n)=>{const s=t.toLowerCase(),r=n.toLowerCase();return s==="default"&&r!=="default"?-1:r==="default"&&s!=="default"?1:t.localeCompare(n)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get agentChatModel(){return this._agentChatModel}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary}get historySummaryParams(){return this._historySummaryParams}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get enableAgentTabs(){return this._enableAgentTabs}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}get enableGroupedTools(){return this._enableGroupedTools}get remoteAgentsResumeHintAvailableTtlDays(){return this._remoteAgentsResumeHintAvailableTtlDays}get enableParallelTools(){return this._enableParallelTools}get enableAgentGitTracker(){return this._enableAgentGitTracker}get memoriesParams(){return this._memoriesParams}}var xh=kl,Ah=/\s/,Rh=function(e){for(var t=e.length;t--&&Ah.test(e.charAt(t)););return t},Mh=/^\s+/,Oh=xl,Ph=Cl,Dh=function(e){return e&&e.slice(0,Rh(e)+1).replace(Mh,"")},xo=br,Lh=function(e){return typeof e=="symbol"||Ph(e)&&Oh(e)=="[object Symbol]"},Fh=/^[-+]0x[0-9a-f]+$/i,Uh=/^0b[01]+$/i,$h=/^0o[0-7]+$/i,qh=parseInt,Hh=br,Rs=function(){return xh.Date.now()},Ao=function(e){if(typeof e=="number")return e;if(Lh(e))return NaN;if(xo(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=xo(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Dh(e);var n=Uh.test(e);return n||$h.test(e)?qh(e.slice(2),n?2:8):Fh.test(e)?NaN:+e},Bh=Math.max,Gh=Math.min,Vh=function(e,t,n){var s,r,a,o,i,c,l=0,d=!1,u=!1,h=!0;if(typeof e!="function")throw new TypeError("Expected a function");function p(S){var v=s,T=r;return s=r=void 0,l=S,o=e.apply(T,v)}function m(S){var v=S-c;return c===void 0||v>=t||v<0||u&&S-l>=a}function y(){var S=Rs();if(m(S))return f(S);i=setTimeout(y,function(v){var T=t-(v-c);return u?Gh(T,a-(v-l)):T}(S))}function f(S){return i=void 0,h&&s?p(S):(s=r=void 0,o)}function b(){var S=Rs(),v=m(S);if(s=arguments,r=this,c=S,v){if(i===void 0)return function(T){return l=T,i=setTimeout(y,t),d?p(T):o}(c);if(u)return clearTimeout(i),i=setTimeout(y,t),p(c)}return i===void 0&&(i=setTimeout(y,t)),o}return t=Ao(t)||0,Hh(n)&&(d=!!n.leading,a=(u="maxWait"in n)?Bh(Ao(n.maxWait)||0,t):a,h="trailing"in n?!!n.trailing:h),b.cancel=function(){i!==void 0&&clearTimeout(i),l=0,s=c=r=i=void 0},b.flush=function(){return i===void 0?o:f(Rs())},b},jh=br;const Yh=_r(function(e,t,n){var s=!0,r=!0;if(typeof e!="function")throw new TypeError("Expected a function");return jh(n)&&(s="leading"in n?!!n.leading:s,r="trailing"in n?!!n.trailing:r),Vh(e,t,{leading:s,maxWait:t,trailing:r})});class Kh{constructor(t){g(this,"SIDECAR_TIMEOUT_MS",5e3);g(this,"getRulesList",async(t=!0)=>{const n={type:Ne.getRulesListRequest,data:{includeGuidelines:t}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.rules});g(this,"createRule",async t=>{const n={type:Ne.createRule,data:{ruleName:t.trim()}};return(await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS)).data.createdRule||null});g(this,"getWorkspaceRoot",async()=>{const t={type:Ne.getWorkspaceRoot};return(await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)).data.workspaceRoot||""});g(this,"updateRuleFile",async(t,n)=>{const s={type:Ne.updateRuleFile,data:{path:t,content:n}};await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS)});g(this,"deleteRule",async(t,n=!0)=>{const s={type:Ne.deleteRule,data:{path:t,confirmed:n}};await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS)});g(this,"processSelectedPaths",async(t,n=!0)=>{const s={type:Ne.processSelectedPathsRequest,data:{selectedPaths:t,autoImport:n}},r=await this._asyncMsgSender.sendToSidecar(s,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:r.data.importedRulesCount,directoryOrFile:r.data.directoryOrFile,errors:r.data.errors}});g(this,"getAutoImportOptions",async()=>{const t={type:Ne.autoImportRules};return await this._asyncMsgSender.sendToSidecar(t,this.SIDECAR_TIMEOUT_MS)});g(this,"processAutoImportSelection",async t=>{const n={type:Ne.autoImportRulesSelectionRequest,data:{selectedLabel:t}},s=await this._asyncMsgSender.sendToSidecar(n,this.SIDECAR_TIMEOUT_MS);return{importedRulesCount:s.data.importedRulesCount,duplicatesCount:s.data.duplicatesCount,totalAttempted:s.data.totalAttempted,source:s.data.source}});this._asyncMsgSender=t}}class pm{constructor(t,n=!0){g(this,"_rulesFiles",Ht([]));g(this,"_loading",Ht(!0));g(this,"_extensionClientRules");g(this,"_requestRulesThrottled",Yh(async()=>{this._loading.set(!0);try{const t=await this._extensionClientRules.getRulesList(this.includeGuidelines);this._rulesFiles.set(t)}catch(t){console.error("Failed to get rules list:",t)}finally{this._loading.set(!1)}},250,{leading:!0,trailing:!0}));this._msgBroker=t,this.includeGuidelines=n,this._extensionClientRules=new Kh(this._msgBroker),this.requestRules()}handleMessageFromExtension(t){return!(!t.data||t.data.type!==E.getRulesListResponse)&&(this._rulesFiles.set(t.data.data),this._loading.set(!1),!0)}async requestRules(){return this._requestRulesThrottled()}async createRule(t){try{const n=await this._extensionClientRules.createRule(t);return await this.requestRules(),n}catch(n){throw console.error("Failed to create rule:",n),n}}async getWorkspaceRoot(){try{return await this._extensionClientRules.getWorkspaceRoot()}catch(t){return console.error("Failed to get workspace root:",t),""}}async updateRuleContent(t){const n=xe.formatRuleFileForMarkdown(t);try{await this._extensionClientRules.updateRuleFile(t.path,n)}catch(s){console.error("Failed to update rule file:",s)}await this.requestRules()}async deleteRule(t){try{await this._extensionClientRules.deleteRule(t,!0),await this.requestRules()}catch(n){throw console.error("Failed to delete rule:",n),n}}async processSelectedPaths(t){try{const n=await this._extensionClientRules.processSelectedPaths(t,!0);return await this.requestRules(),n}catch(n){throw console.error("Failed to process selected paths:",n),n}}async getAutoImportOptions(){return await this._extensionClientRules.getAutoImportOptions()}async processAutoImportSelection(t){try{const n=await this._extensionClientRules.processAutoImportSelection(t.label);return await this.requestRules(),n}catch(n){throw console.error("Failed to process auto-import selection:",n),n}}getCachedRules(){return this._rulesFiles}getLoading(){return this._loading}}var Wh=Po('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor"></path></svg>');function zh(e){var t=Wh();O(e,t)}const qt=class qt{constructor(t=void 0){g(this,"_lastFocusAnchorElement");g(this,"_focusedIndexStore",Ht(void 0));g(this,"focusedIndex",this._focusedIndexStore);g(this,"_rootElement");g(this,"_triggerElement");g(this,"_getItems",()=>{var s;const t=(s=this._rootElement)==null?void 0:s.querySelectorAll(`.${qt.ITEM_CLASS}`),n=t==null?void 0:t[0];return n instanceof HTMLElement&&this._recomputeFocusAnchor(n),Array.from(t??[])});g(this,"_recomputeFocusAnchor",t=>{var a;const n=(a=this._parentContext)==null?void 0:a._getItems(),s=n==null?void 0:n.indexOf(t);if(s===void 0||n===void 0)return;const r=Math.max(s-1,0);this._lastFocusAnchorElement=n[r]});g(this,"registerRoot",t=>{this._rootElement=t,t.addEventListener("keydown",this._onKeyDown);const n=()=>{this.getCurrentFocusedIdx()},s=r=>{t.contains(r.relatedTarget)||this._focusedIndexStore.set(void 0)};return t.addEventListener("focusin",n),t.addEventListener("focusout",s),this._getItems(),{destroy:()=>{this._removeFromTrapStack(),this._rootElement=void 0,t.removeEventListener("keydown",this._onKeyDown),t.removeEventListener("focusin",n),t.removeEventListener("focusout",s),this._focusedIndexStore.set(void 0)}}});g(this,"registerTrigger",t=>(this._triggerElement=t.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])')??t,{destroy:()=>{this._triggerElement=void 0}}));g(this,"_onKeyDown",t=>{var n;switch(t.key){case"ArrowUp":t.preventDefault(),this.focusPrev();break;case"ArrowDown":t.preventDefault(),this.focusNext();break;case"ArrowLeft":this._requestClose();break;case"ArrowRight":this.clickFocusedItem();break;case"Tab":{const s=this.getCurrentFocusedIdx();if(s===void 0||this.parentContext)break;(!t.shiftKey&&s===this._getItems().length-1||t.shiftKey&&s===0)&&(t.preventDefault(),(n=this._triggerElement)==null||n.focus());break}}});g(this,"_requestClose",()=>{var t;(t=this._rootElement)==null||t.dispatchEvent(new Al)});g(this,"getCurrentFocusedIdx",()=>{const t=this._getItems().findIndex(s=>s===document.activeElement),n=t===-1?void 0:t;return this._focusedIndexStore.set(n),n});g(this,"setFocusedIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=dn(t,n.length);this._focusedIndexStore.set(s)});g(this,"focusIdx",t=>{const n=this._getItems();if(n.length===0)return void this._focusedIndexStore.set(void 0);const s=dn(t,n.length),r=n[s];r==null||r.focus(),this._focusedIndexStore.set(s)});g(this,"popNestedFocus",()=>{if(this._parentContext){this._focusedIndexStore.set(void 0);const t=this._lastFocusAnchorElement,n=t?this._parentContext._getItems().indexOf(t):void 0;return n===void 0?(this._parentContext.focusIdx(0),!0):(this._parentContext.focusIdx(n),!0)}return!1});g(this,"focusNext",()=>{const t=this._getItems();if(t.length===0)return;const n=dn(t.findIndex(s=>s===document.activeElement)+1,t.length);t[n].focus(),this._focusedIndexStore.set(n)});g(this,"focusPrev",()=>{var s;const t=this._getItems();if(t.length===0)return;const n=dn(t.findIndex(r=>r===document.activeElement)-1,t.length);(s=t[n])==null||s.focus(),this._focusedIndexStore.set(n)});g(this,"clickFocusedItem",async()=>{const t=document.activeElement;t&&(t.click(),await Os())});g(this,"_addToTrapStack",()=>{this._rootElement&&Gr.add(this._rootElement)});g(this,"_removeFromTrapStack",()=>{this._rootElement&&Gr.remove(this._rootElement)});g(this,"handleOpenChange",t=>{t?this._addToTrapStack():this._removeFromTrapStack()});this._parentContext=t}get rootElement(){return this._rootElement}get triggerElement(){return this._triggerElement}get parentContext(){return this._parentContext}};g(qt,"CONTEXT_KEY","augment-dropdown-menu-focus"),g(qt,"ITEM_CLASS","js-dropdown-menu__focusable-item");let de=qt;function dn(e,t){return(e%t+t)%t}const mt="augment-dropdown-menu-content";var Xh=re("<div><!></div>"),Jh=re('<div class="l-dropdown-menu-augment__container svelte-o54ind"><!></div>');function Ro(e,t){_e(t,!1);const[n,s]=Be(),r=()=>ke(p,"$sizeState",n),a=Re();let o=x(t,"size",8,2),i=x(t,"onEscapeKeyDown",8,()=>{}),c=x(t,"onClickOutside",8,()=>{}),l=x(t,"onRequestClose",8,()=>{}),d=x(t,"side",8,"top"),u=x(t,"align",8,"center");const h={size:Ht(o())},p=h.size;Do(mt,h);const m=oe(de.CONTEXT_KEY),y=oe(hn.CONTEXT_KEY);Ae(()=>Ke(o()),()=>{p.set(o())}),Ae(()=>{},()=>{Tl(Me(a,y.state),"$openState",n)}),gt(),be(),we("keydown",El,function(f){if(ke(F(a),"$openState",n).open&&f.key==="Tab"&&!f.shiftKey){if(m.getCurrentFocusedIdx()!==void 0)return;f.preventDefault(),m==null||m.focusIdx(0)}}),Rl(e,{onEscapeKeyDown:i(),onClickOutside:c(),onRequestClose:l(),get side(){return d()},get align(){return u()},$$events:{keydown(f){Le.call(this,t,f)}},children:(f,b)=>{var S=Jh(),v=ue(S);Ml(v,{get size(){return r()},insetContent:!0,includeBackground:!1,children:(T,w)=>{var I=Xh(),k=ue(I);se(k,t,"default",{},null),Ms(I,U=>{var D;return(D=m.registerRoot)==null?void 0:D.call(m,U)}),ft(()=>yt(I,1,`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${r()}`,"svelte-o54ind")),O(T,I)},$$slots:{default:!0}}),O(f,S)},$$slots:{default:!0}}),ve(),s()}var Zh=re('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),Qh=re('<div class="c-dropdown-menu-augment__item-icon svelte-48oly1"><!></div>'),ep=re("<!> <!> <!>",1);function yr(e,t){const n=Lo(t),s=W(t,["children","$$slots","$$events","$$legacy"]),r=W(s,["highlight","disabled","color","onSelect"]);_e(t,!1);const[a,o]=Be(),i=()=>ke(b,"$sizeState",a),c=Re(),l=Re(),d=Re();let u=x(t,"highlight",24,()=>{}),h=x(t,"disabled",24,()=>{}),p=x(t,"color",24,()=>{}),m=x(t,"onSelect",8,()=>{});const y=oe(mt),f=oe(de.CONTEXT_KEY),b=y.size;function S(k){var N;if(h())return;const U=(N=f.rootElement)==null?void 0:N.querySelectorAll(`.${de.ITEM_CLASS}`);if(!U)return;const D=Array.from(U).findIndex(X=>X===k);D!==-1&&f.setFocusedIdx(D)}Ae(()=>(F(c),F(l),Ke(r)),()=>{Me(c,r.class),Me(l,Uo(r,["class"]))}),Ae(()=>(Ke(h()),Ke(u()),F(c)),()=>{Me(d,[h()?"":de.ITEM_CLASS,"c-dropdown-menu-augment__item",u()?"c-dropdown-menu-augment__item--highlighted":"",F(c)].join(" "))}),gt(),be();const v=wn(()=>p()??"neutral"),T=wn(()=>!p());var w=Vr(()=>jr("dropdown-menu-item","highlighted",u())),I=Vr(()=>jr("dropdown-menu-item","disabled",h()));ml(e,et({get class(){return F(d)},get size(){return i()},variant:"ghost",get color(){return F(v)},get highContrast(){return F(T)},alignment:"left",get disabled(){return h()}},()=>F(w),()=>F(I),()=>F(l),{$$events:{click:k=>{k.currentTarget instanceof HTMLElement&&S(k.currentTarget),m()(k)},mouseover:k=>{k.currentTarget instanceof HTMLElement&&S(k.currentTarget)},mousedown:k=>{k.preventDefault(),k.stopPropagation()}},children:(k,U)=>{var D=ep(),N=Ce(D),X=B=>{var ye=Zh(),Oe=ue(ye);se(Oe,t,"iconLeft",{},null),O(B,ye)};Mt(N,B=>{tt(()=>n.iconLeft)&&B(X)});var at=Ot(N,2);Fo(at,{get size(){return i()},children:(B,ye)=>{var Oe=Ge(),Xt=Ce(Oe);se(Xt,t,"default",{},null),O(B,Oe)},$$slots:{default:!0}});var A=Ot(at,2),fe=B=>{var ye=Qh(),Oe=ue(ye);se(Oe,t,"iconRight",{},null),O(B,ye)};Mt(A,B=>{tt(()=>n.iconRight)&&B(fe)}),O(k,D)},$$slots:{default:!0}})),ve(),o()}var tp=Po("<svg><!></svg>");function np(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]);var s=tp();$o(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 320 512",...n}));var r=ue(s);gl(r,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M305 239c9.4 9.4 9.4 24.6 0 33.9L113 465c-9.4 9.4-24.6 9.4-33.9 0s-9.4-24.6 0-33.9l175-175L79 81c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0z"/>',!0),O(e,s)}function Mo(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);yr(e,et({class:"c-dropdown-menu-augment__breadcrumb-chevron"},()=>s,{children:(r,a)=>{var o=Ge(),i=Ce(o);se(i,t,"default",{},null),O(r,o)},$$slots:{default:!0,iconRight:(r,a)=>{np(r,{slot:"iconRight"})}}}))}var sp=re("<div><!></div>");function Oo(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,["requestOpen","requestClose","focusIdx","setFocusedIdx","getCurrentFocusedIdx","focusedIndex","defaultOpen","open","onOpenChange","delayDurationMs","nested","onHoverStart","onHoverEnd","triggerOn"]);_e(t,!1);let r=x(t,"defaultOpen",24,()=>{}),a=x(t,"open",24,()=>{}),o=x(t,"onOpenChange",24,()=>{}),i=x(t,"delayDurationMs",24,()=>{}),c=x(t,"nested",24,()=>{}),l=x(t,"onHoverStart",8,()=>{}),d=x(t,"onHoverEnd",8,()=>{}),u=x(t,"triggerOn",24,()=>[pn.Click]),h=Re();const p=()=>{var w;return(w=F(h))==null?void 0:w.requestOpen()},m=()=>{var w;return(w=F(h))==null?void 0:w.requestClose()},y=w=>v.focusIdx(w),f=w=>v.setFocusedIdx(w),b=()=>v.getCurrentFocusedIdx(),S=oe(de.CONTEXT_KEY),v=new de(S);Do(de.CONTEXT_KEY,v);const T=v.focusedIndex;return be(),qo(Ol(e,et({get defaultOpen(){return r()},get open(){return a()},onOpenChange:function(w){var I;v.handleOpenChange(w),(I=o())==null||I(w)},get delayDurationMs(){return i()},onHoverStart:l(),onHoverEnd:d(),get triggerOn(){return u()},get nested(){return c()}},()=>s,{children:(w,I)=>{var k=Ge(),U=Ce(k);se(U,t,"default",{},null),O(w,k)},$$slots:{default:!0},$$legacy:!0})),w=>Me(h,w),()=>F(h)),ot(t,"requestOpen",p),ot(t,"requestClose",m),ot(t,"focusIdx",y),ot(t,"setFocusedIdx",f),ot(t,"getCurrentFocusedIdx",b),ot(t,"focusedIndex",T),ve({requestOpen:p,requestClose:m,focusIdx:y,setFocusedIdx:f,getCurrentFocusedIdx:b,focusedIndex:T})}var rp=re("<div></div>");function ap(e,t){let n=x(t,"size",8,1),s=x(t,"orientation",8,"horizontal"),r=x(t,"useCurrentColor",8,!1),a=x(t,"class",8,"");var o=rp();let i;ft(c=>i=yt(o,1,`c-separator c-separator--size-${n()===.5?"0_5":n()} c-separator--orientation-${s()} ${a()}`,"svelte-o0csoy",i,c),[()=>({"c-separator--current-color":r()})],wn),O(e,o)}var op=re("<div><!></div>"),ip=re('<label class="c-text-field-label svelte-vuqlvc"><!></label>'),lp=re('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),up=re('<div class="c-text-field__slot c-base-text-input__slot"><!></div>'),cp=re("<!> <input/> <!>",1),dp=re("<div><!> <!></div>");function hp(e,t){const n=Lo(t),s=W(t,["children","$$slots","$$events","$$legacy"]),r=W(s,["variant","size","color","textInput","value","id"]);_e(t,!1);const a=Re(),o=Re(),i=Re(),c=Il();let l=x(t,"variant",8,"surface"),d=x(t,"size",8,2),u=x(t,"color",24,()=>{}),h=x(t,"textInput",28,()=>{}),p=x(t,"value",12,""),m=x(t,"id",24,()=>{});const y=`text-field-${Math.random().toString(36).substring(2,11)}`;function f(w){c("change",w)}Ae(()=>Ke(m()),()=>{Me(a,m()||y)}),Ae(()=>(F(o),F(i),Ke(r)),()=>{Me(o,r.class),Me(i,Uo(r,["class"]))}),gt(),be();var b=dp();yt(b,1,"c-text-field svelte-vuqlvc",null,{},{"c-text-field--has-left-icon":n.iconLeft!==void 0,"c-text-field--has-right-icon":n.iconRight!==void 0});var S=ue(b),v=w=>{var I=ip(),k=ue(I);se(k,t,"label",{},null),ft(()=>Nl(I,"for",F(a))),O(w,I)};Mt(S,w=>{tt(()=>n.label)&&w(v)});var T=Ot(S,2);Pl(T,{get variant(){return l()},get size(){return d()},get color(){return u()},children:(w,I)=>{var k=cp(),U=Ce(k),D=A=>{var fe=lp(),B=ue(fe);se(B,t,"iconLeft",{},null),O(A,fe)};Mt(U,A=>{tt(()=>n.iconLeft)&&A(D)});var N=Ot(U,2);$o(N,()=>({spellCheck:"false",class:`c-text-field__input c-base-text-input__input ${F(o)}`,id:F(a),...F(i)}),void 0,"svelte-vuqlvc"),qo(N,A=>h(A),()=>h());var X=Ot(N,2),at=A=>{var fe=up(),B=ue(fe);se(B,t,"iconRight",{},null),O(A,fe)};Mt(X,A=>{tt(()=>n.iconRight)&&A(at)}),Dl(N,p),we("change",N,f),we("click",N,function(A){Le.call(this,t,A)}),we("keydown",N,function(A){Le.call(this,t,A)}),we("input",N,function(A){Le.call(this,t,A)}),we("blur",N,function(A){Le.call(this,t,A)}),we("dblclick",N,function(A){Le.call(this,t,A)}),we("focus",N,function(A){Le.call(this,t,A)}),we("mouseup",N,function(A){Le.call(this,t,A)}),we("selectionchange",N,function(A){Le.call(this,t,A)}),O(w,k)},$$slots:{default:!0}}),O(e,b),ve()}var pp=re("<div><!></div>"),mp=re("<div><!></div>");const mm={BreadcrumbBackItem:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);yr(e,et({class:"c-dropdown-menu-augment__breadcrumb-back-chevron"},()=>s,{children:(r,a)=>{var o=Ge(),i=Ce(o);se(i,t,"default",{},null),O(r,o)},$$slots:{default:!0,iconLeft:(r,a)=>{zh(r)}}}))},BreadcrumbItem:Mo,Content:Ro,Item:yr,Label:function(e,t){_e(t,!1);const[n,s]=Be(),r=()=>ke(o,"$sizeState",n),a=Re(),o=oe(mt).size;Ae(()=>r(),()=>{Me(a,["c-dropdown-menu-augment__label-item",`c-dropdown-menu-augment__label-item--size-${r()}`].join(" "))}),gt(),be();var i=sp(),c=ue(i);Fo(c,{get size(){return r()},weight:"regular",children:(l,d)=>{var u=Ge(),h=Ce(u);se(h,t,"default",{},null),O(l,u)},$$slots:{default:!0}}),ft(()=>yt(i,1,Yr(F(a)),"svelte-gehsvg")),O(e,i),ve(),s()},Root:Oo,Separator:function(e,t){_e(t,!1);const[n,s]=Be(),r=oe(mt).size;be();var a=op();ap(ue(a),{size:4,orientation:"horizontal"}),ft(()=>yt(a,1,`c-dropdown-menu-augment__separator c-dropdown-menu-augment__separator--size-${ke(r,"$sizeState",n)}`,"svelte-24h9u")),O(e,a),ve(),s()},Sub:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);_e(t,!1),be();const r=wn(()=>(Ke(pn),tt(()=>[pn.Click,pn.Hover])));Oo(e,et({nested:!0,get triggerOn(){return F(r)}},()=>s,{children:(a,o)=>{var i=Ge(),c=Ce(i);se(c,t,"default",{},null),O(a,i)},$$slots:{default:!0}})),ve()},SubContent:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,[]);_e(t,!1);const[r,a]=Be(),o=()=>ke(d,"$didOpen",r),i=oe(mt).size,c=oe(de.CONTEXT_KEY),l=oe(hn.CONTEXT_KEY),d=wl(l.state,u=>u.open);Ae(()=>(o(),Os),()=>{o()&&Os().then(()=>c==null?void 0:c.focusIdx(0))}),Ae(()=>o(),()=>{!o()&&(c==null||c.popNestedFocus())}),gt(),be(),Ro(e,et(()=>s,{side:"right",align:"start",get size(){return ke(i,"$sizeState",r)},children:(u,h)=>{var p=Ge(),m=Ce(p);se(m,t,"default",{},null),O(u,p)},$$slots:{default:!0}})),ve(),a()},SubTrigger:function(e,t){_e(t,!1);const[n,s]=Be(),r=oe(hn.CONTEXT_KEY).state;be(),Kr(e,{children:(a,o)=>{Mo(a,{get highlight(){return ke(r,"$stateStore",n).open},children:(i,c)=>{var l=Ge(),d=Ce(l);se(d,t,"default",{},null),O(i,l)},$$slots:{default:!0}})},$$slots:{default:!0}}),ve(),s()},TextFieldItem:function(e,t){const n=W(t,["children","$$slots","$$events","$$legacy"]),s=W(n,["value"]);_e(t,!1);const[r,a]=Be(),o=()=>ke(l,"$sizeState",r),i=Re();let c=x(t,"value",12,"");const l=oe(mt).size;Ae(()=>o(),()=>{Me(i,["c-dropdown-menu-augment__text-field-item",`c-dropdown-menu-augment__text-field-item--size-${o()}`].join(" "))}),gt(),be();var d=pp();hp(ue(d),et({get class(){return Ke(de),tt(()=>de.ITEM_CLASS)},get size(){return o()}},()=>s,{get value(){return c()},set value(u){c(u)},$$legacy:!0})),ft(()=>yt(d,1,Yr(F(i)),"svelte-1xu00bc")),O(e,d),ve(),a()},Trigger:function(e,t){_e(t,!1);const[n,s]=Be(),r=()=>ke(c,"$openState",n);let a=x(t,"referenceClientRect",24,()=>{});const o=oe(de.CONTEXT_KEY),i=oe(hn.CONTEXT_KEY),c=i.state;be(),Kr(e,{get referenceClientRect(){return a()},$$events:{keydown:async l=>{switch(l.key){case"ArrowUp":l.preventDefault(),l.stopPropagation(),r().open||await o.clickFocusedItem(),o==null||o.focusIdx(-1);break;case"ArrowDown":l.preventDefault(),l.stopPropagation(),r().open||await o.clickFocusedItem(),o==null||o.focusIdx(0);break;case"Enter":l.preventDefault(),l.stopPropagation(),o==null||o.clickFocusedItem()}}},children:(l,d)=>{var u=mp(),h=ue(u);se(h,t,"default",{},null),Ms(u,p=>{var m;return(m=o.registerTrigger)==null?void 0:m.call(o,p)}),Ms(u,p=>{var m;return(m=i.registerTrigger)==null?void 0:m.call(i,p)}),O(l,u)},$$slots:{default:!0}}),ve(),s()}};export{Op as $,is as A,gc as B,hm as C,mm as D,lm as E,Bu as F,xp as G,md as H,Pp as I,fc as J,Di as K,_c as L,Gu as M,Cc as N,vc as O,Ec as P,Sc as Q,pm as R,te as S,hp as T,yr as U,Lp as V,Dp as W,kp as X,Ai as Y,Rp as Z,Mp as _,xe as a,Vp as a$,Cp as a0,yc as a1,Tc as a2,np as a3,Vu as a4,Wp as a5,ec as a6,Yh as a7,$p as a8,Qe as a9,ac as aA,rc as aB,ce as aC,Jp as aD,em as aE,En as aF,Co as aG,zh as aH,sc as aI,nc as aJ,pt as aK,tc as aL,Up as aM,Oi as aN,zu as aO,qp as aP,dd as aQ,ji as aR,Gp as aS,Bp as aT,qr as aU,hd as aV,pd as aW,An as aX,kc as aY,Pi as aZ,jp as a_,Mi as aa,Ku as ab,xc as ac,Yu as ad,No as ae,Nc as af,Ic as ag,Fp as ah,Hp as ai,Xp as aj,dm as ak,Zu as al,Qu as am,yh as an,Ip as ao,Ap as ap,ap as aq,xn as ar,Kp as as,Sh as at,zp as au,Eh as av,$t as aw,Qp as ax,Th as ay,Zp as az,Xu as b,ic as b0,oc as b1,sm as b2,im as b3,am as b4,rm as b5,om as b6,nm as b7,tm as b8,C as c,M as d,Ju as e,Wu as f,ut as g,xt as h,j as i,H as j,ht as k,vh as l,le as m,Ch as n,cm as o,um as p,xs as q,Tt as r,Yp as s,uc as t,cc as u,dc as v,hc as w,Np as x,pc as y,mc as z};
