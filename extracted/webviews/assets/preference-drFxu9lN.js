import{B as b,F as h,J,t as o,Q as a,T as L,M as gt,ab as P,S as A,b as p,Z as et,V as ft,A as dt,O as yt,m,ak as wt,aE as pt,L as t,C as c,_ as U,P as tt,a0 as kt,D as mt,G as At,N as ht,I as bt,al as $t,H as qt,az as Bt}from"./SpinnerAugment-DwpTQqCj.js";import{c as B,W as H}from"./IconButtonAugment-CpHf993c.js";import{aJ as lt}from"./AugmentMessage-DpP2I4-b.js";import{b as Ct,a as xt}from"./BaseTextInput-D9ZtAsmP.js";import{C as Mt,S as Rt}from"./folder-opened-D7I-QOBo.js";import{M as St}from"./message-broker-CJi4HhV6.js";import{s as It}from"./chat-model-context-52iR26yw.js";import{M as Dt}from"./index-BxJ3Yi_l.js";import"./CalloutAugment-BhO9fdsc.js";import"./CardAugment-C6Oa90En.js";import"./index-CF0COlDh.js";import"./async-messaging-3euWO_Vj.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CKc3PkF0.js";import"./isObjectLike-BdluLIvS.js";import"./index-BddLnVwz.js";import"./diff-operations-jldzuAhI.js";import"./svelte-component-OFA3-Jvi.js";import"./Filespan-CZ9Yk5fv.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./keypress-DD1aQVr0.js";import"./await-SODcoE9a.js";import"./OpenFileButton-rvNFtINp.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-DIrnFfVT.js";import"./ra-diff-ops-model-CrGPikPJ.js";import"./TextAreaAugment-o5eN7Hov.js";import"./ButtonAugment-UYn0l1qJ.js";import"./CollapseButtonAugment-CLwuB2Wy.js";import"./user-DJbhSuGe.js";import"./MaterialIcon-BspaVi72.js";import"./CopyButton-BsD73rhm.js";import"./copy-DHWxo32G.js";import"./ellipsis-mzD6NhIP.js";import"./LanguageIcon-Dnvdydi_.js";import"./chevron-down-CifcRB5W.js";import"./index-McJAeurX.js";import"./augment-logo-8QzK3Xj7.js";import"./pen-to-square-DcsW-Net.js";import"./check-8SJSJBbK.js";var Pt=h('<div class="header svelte-1894wv4"> </div>'),Wt=h('<div class="container svelte-1894wv4"><!> <div class="buttons svelte-1894wv4"><button type="button">A</button> <button type="button">A</button> <button type="button">A</button> <button type="button">=</button> <button type="button">B</button> <button type="button">B</button> <button type="button">B</button></div></div>');function nt(N,C){let i=b(C,"selected",12,null),x=b(C,"question",8,null);function v(w){i(w)}var e=Wt(),d=o(e),W=w=>{var z=Pt(),j=o(z);L(()=>et(j,x())),p(w,z)};J(d,w=>{x()&&w(W)});var M=a(d,2),g=o(M);let f;var r=a(g,2);let $;var y=a(r,2);let s;var R=a(y,2);let V;var _=a(R,2);let X;var O=a(_,2);let Q;var T=a(O,2);let Y;L((w,z,j,at,st,l,u)=>{f=P(g,1,"button large svelte-1894wv4",null,f,w),$=P(r,1,"button medium svelte-1894wv4",null,$,z),s=P(y,1,"button small svelte-1894wv4",null,s,j),V=P(R,1,"button equal svelte-1894wv4",null,V,at),X=P(_,1,"button small svelte-1894wv4",null,X,st),Q=P(O,1,"button medium svelte-1894wv4",null,Q,l),Y=P(T,1,"button large svelte-1894wv4",null,Y,u)},[()=>({highlighted:i()==="A3"}),()=>({highlighted:i()==="A2"}),()=>({highlighted:i()==="A1"}),()=>({highlighted:i()==="="}),()=>({highlighted:i()==="B1"}),()=>({highlighted:i()==="B2"}),()=>({highlighted:i()==="B3"})],gt),A("click",g,()=>v("A3")),A("click",r,()=>v("A2")),A("click",y,()=>v("A1")),A("click",R,()=>v("=")),A("click",_,()=>v("B1")),A("click",O,()=>v("B2")),A("click",T,()=>v("B3")),p(N,e)}var _t=h('<div class="question svelte-1i0f73l"> </div>'),Ot=h('<div class="container svelte-1i0f73l"><!> <textarea class="input svelte-1i0f73l" rows="3"></textarea></div>'),zt=h('<button class="button svelte-2k5n"> </button>'),Et=h("<div> </div>"),Ft=h('<div class="container svelte-n0uy88"><!> <label class="custom-checkbox svelte-n0uy88"><input type="checkbox" class="svelte-n0uy88"/> <span class="svelte-n0uy88"></span></label></div>'),Lt=h("<!> <!> <!> <!> <!> <!>",1),Ht=h("<p>Streaming in progress... Please wait for both responses to complete.</p>"),Jt=h('<main><div class="l-pref svelte-751nif"><h1 class="svelte-751nif">Input message</h1> <!> <hr class="l-side-by-side svelte-751nif"/> <div class="l-side-by-side svelte-751nif"><div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option A</h1> <!></div> <div class="divider svelte-751nif"></div> <div class="l-side-by-side__child svelte-751nif"><h1 class="svelte-751nif">Option B</h1> <!></div></div> <hr class="svelte-751nif"/> <!></div></main>');function Nt(N,C){dt(C,!1);const i=m(),x=m(),v=m();let e=b(C,"inputData",8);const d=yt();let W=new Mt(new St(B),B,new Rt);It(W);let M=m(null),g=m(null),f=null,r=m(null),$=m(""),y=m(!1),s=m({a:null,b:null}),R=m(e().data.a.response.length>0&&e().data.b.response.length>0);function V(){if(f="=",t(r)===null)return void d("notify","Overall rating is required");const l={overallRating:t(r),formattingRating:t(M)||"=",hallucinationRating:f||"=",instructionFollowingRating:t(g)||"=",isHighQuality:t(y),textFeedback:t($)};d("result",l)}wt(()=>{window.addEventListener("message",l=>{const u=l.data;u.type===H.chatModelReply?(u.stream==="A"?pt(s,t(s).a=u.data.text):u.stream==="B"&&pt(s,t(s).b=u.data.text),c(s,t(s))):u.type===H.chatStreamDone&&c(R,!0)})}),U(()=>t(r),()=>{var l;c(i,(l=t(r))==="="||l===null?"Is this a high quality comparison?":`Are you completely happy with response '${l.startsWith("A")?"A":"B"}'?`)}),U(()=>(t(s),tt(e())),()=>{c(x,t(s).a!==null?t(s).a:e().data.a.response)}),U(()=>(t(s),tt(e())),()=>{c(v,t(s).b!==null?t(s).b:e().data.b.response)}),U(()=>tt(e()),()=>{c(R,e().data.a.response.length>0&&e().data.b.response.length>0)}),kt(),mt();var _=Jt(),X=o(_),O=a(o(X),2);lt(O,{get markdown(){return tt(e()),At(()=>e().data.a.message)}});var Q=a(O,4),T=o(Q),Y=a(o(T),2);lt(Y,{get markdown(){return t(x)}});var w=a(T,4),z=a(o(w),2);lt(z,{get markdown(){return t(v)}});var j=a(Q,4),at=l=>{var u=Lt(),ot=bt(u);nt(ot,{question:"Which response is formatted better? (e.g. level of detail style, structure)?",get selected(){return t(M)},set selected(n){c(M,n)},$$legacy:!0});var rt=a(ot,2);nt(rt,{question:"Which response follows your instruction better?",get selected(){return t(g)},set selected(n){c(g,n)},$$legacy:!0});var ut=a(rt,2);nt(ut,{question:"Which response is better overall?",get selected(){return t(r)},set selected(n){c(r,n)},$$legacy:!0});var vt=a(ut,2);(function(n,k){let G=b(k,"isChecked",12,!1),S=b(k,"question",8,null);var q=Ft(),I=o(q),E=D=>{var K=Et(),it=o(K);L(()=>et(it,S())),p(D,K)};J(I,D=>{S()&&D(E)});var F=a(I,2),Z=o(F);xt(Z,G),p(n,q)})(vt,{get question(){return t(i)},get isChecked(){return t(y)},set isChecked(n){c(y,n)},$$legacy:!0});var ct=a(vt,2);(function(n,k){let G=b(k,"value",12,""),S=b(k,"question",8,null),q=b(k,"placeholder",8,"");var I=Ot(),E=o(I),F=D=>{var K=_t(),it=o(K);L(()=>et(it,S())),p(D,K)};J(E,D=>{S()&&D(F)});var Z=a(E,2);L(()=>ft(Z,"placeholder",q())),Ct(Z,G),p(n,I)})(ct,{question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions.",get value(){return t($)},set value(n){c($,n)},$$legacy:!0}),function(n,k){let G=b(k,"label",8,"Submit"),S=b(k,"onClick",8);var q=zt(),I=o(q);L(()=>et(I,G())),A("click",q,function(...E){var F;(F=S())==null||F.apply(this,E)}),p(n,q)}(a(ct,2),{label:"Submit",onClick:V}),p(l,u)},st=l=>{var u=Ht();p(l,u)};J(j,l=>{t(R)?l(at):l(st,!1)}),p(N,_),ht()}var Qt=h("<main><!></main>");function Tt(N,C){dt(C,!1);let i=m();function x(e){const d=e.detail;B.postMessage({type:H.preferenceResultMessage,data:d})}function v(e){B.postMessage({type:H.preferenceNotify,data:e.detail})}B.postMessage({type:H.preferencePanelLoaded}),mt(),A("message",$t,function(e){const d=e.data;d.type===H.preferenceInit&&c(i,d.data)}),Dt.Root(N,{children:(e,d)=>{var W=Qt(),M=o(W),g=f=>{var r=qt(),$=bt(r),y=s=>{Nt(s,{get inputData(){return t(i)},$$events:{result:x,notify:v}})};J($,s=>{t(i).type==="Chat"&&s(y)}),p(f,r)};J(M,f=>{t(i)&&f(g)}),p(e,W)},$$slots:{default:!0}}),ht()}(async function(){B&&B.initialize&&await B.initialize(),Bt(Tt,{target:document.getElementById("app")})})();
