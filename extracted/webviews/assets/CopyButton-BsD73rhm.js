import{z as j,A as B,B as s,C as v,m as D,D as F,F as G,G as d,H as u,I as p,J as H,b as i,K as y,L as C,M as I,t as J,N as K}from"./SpinnerAugment-DwpTQqCj.js";import{C as M}from"./copy-DHWxo32G.js";import{S as V}from"./TextAreaAugment-o5eN7Hov.js";var E=G('<span class="c-copy-button svelte-tq93gm"><!></span>');function U(x,t){const g=j(t);B(t,!1);let b=s(t,"size",8,1),w=s(t,"variant",8,"ghost-block"),z=s(t,"color",8,"neutral"),r=s(t,"text",24,()=>{}),N=s(t,"tooltip",8,"Copy"),h=s(t,"stickyColor",8,!1),L=s(t,"onCopy",8,async()=>{if(r()!==void 0){v(n,!0);try{await Promise.all([navigator.clipboard.writeText(typeof r()=="string"?r():await r()()),(e=250,new Promise(a=>setTimeout(a,e,l)))])}finally{v(n,!1)}var e,l;return"success"}}),P=s(t,"tooltipNested",24,()=>{}),n=D(!1);F();var f=E(),T=J(f);const $=I(()=>({neutral:N(),success:"Copied!"}));V(T,{get defaultColor(){return z()},get size(){return b()},get variant(){return w()},get loading(){return C(n)},get stickyColor(){return h()},get tooltip(){return C($)},stateVariant:{success:"soft"},onClick:L(),icon:d(()=>!g.text),get tooltipNested(){return P()},children:(e,l)=>{var a=u(),c=p(a);y(c,t,"text",{},null),i(e,a)},$$slots:{default:!0,iconLeft:(e,l)=>{var a=u(),c=p(a),q=o=>{var m=u(),S=p(m);y(S,t,"icon",{},null),i(o,m)},A=o=>{M(o,{})};H(c,o=>{d(()=>g.icon)?o(q):o(A,!1)}),i(e,a)}}}),i(x,f),K()}export{U as C};
