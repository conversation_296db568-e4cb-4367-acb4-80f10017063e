import{_ as o,l as p,I as e,e as a,K as s}from"./AugmentMessage-DpP2I4-b.js";import{p as n}from"./gitGraph-YCYPL57B-CKZ4yaAs.js";import"./SpinnerAugment-DwpTQqCj.js";import"./CalloutAugment-BhO9fdsc.js";import"./CardAugment-C6Oa90En.js";import"./IconButtonAugment-CpHf993c.js";import"./index-CF0COlDh.js";import"./async-messaging-3euWO_Vj.js";import"./message-broker-CJi4HhV6.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CKc3PkF0.js";import"./isObjectLike-BdluLIvS.js";import"./BaseTextInput-D9ZtAsmP.js";import"./index-BddLnVwz.js";import"./diff-operations-jldzuAhI.js";import"./svelte-component-OFA3-Jvi.js";import"./Filespan-CZ9Yk5fv.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BxJ3Yi_l.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-D7I-QOBo.js";import"./await-SODcoE9a.js";import"./OpenFileButton-rvNFtINp.js";import"./chat-model-context-52iR26yw.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-DIrnFfVT.js";import"./ra-diff-ops-model-CrGPikPJ.js";import"./TextAreaAugment-o5eN7Hov.js";import"./ButtonAugment-UYn0l1qJ.js";import"./CollapseButtonAugment-CLwuB2Wy.js";import"./user-DJbhSuGe.js";import"./MaterialIcon-BspaVi72.js";import"./CopyButton-BsD73rhm.js";import"./copy-DHWxo32G.js";import"./ellipsis-mzD6NhIP.js";import"./LanguageIcon-Dnvdydi_.js";import"./chevron-down-CifcRB5W.js";import"./index-McJAeurX.js";import"./augment-logo-8QzK3Xj7.js";import"./pen-to-square-DcsW-Net.js";import"./check-8SJSJBbK.js";import"./_baseUniq-Br3yl8DR.js";import"./_basePickBy-DLTCDoex.js";import"./clone-Bh6WuRM6.js";var d={version:s},or={parser:{parse:o(async r=>{const t=await n("info",r);p.debug(t)},"parse")},db:{getVersion:o(()=>d.version,"getVersion")},renderer:{draw:o((r,t,m)=>{p.debug(`rendering info diagram
`+r);const i=e(t);a(i,100,400,!0),i.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${m}`)},"draw")}};export{or as diagram};
