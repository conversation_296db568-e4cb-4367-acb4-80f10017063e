import{l as I,A as R,B as s,a6 as U,D as W,F as T,I as ee,K as g,t as o,Q as r,a7 as te,J as S,L as $,m as F,b as f,N as se,C as h,X as K,Y as D,T as ae,Z as re}from"./SpinnerAugment-DwpTQqCj.js";import"./IconButtonAugment-CpHf993c.js";import"./BaseTextInput-D9ZtAsmP.js";import{T as ne}from"./TextAreaAugment-o5eN7Hov.js";import{l as oe}from"./chevron-down-CifcRB5W.js";var ce=T('<div class="l-markdown-editor svelte-1dcrmc3"><div class="c-markdown-editor__header svelte-1dcrmc3"><!></div> <!> <div class="c-markdown-editor__content svelte-1dcrmc3"><!> <!></div></div> <div class="c-markdown-editor__status svelte-1dcrmc3"><!> <!></div>',1);function me(V,t){const A=I(t,["children","$$slots","$$events","$$legacy"]),B=I(A,["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"]);R(t,!1);let y,C=s(t,"variant",8,"surface"),J=s(t,"size",8,2),L=s(t,"color",24,()=>{}),M=s(t,"resize",8,"none"),a=s(t,"textInput",28,()=>{}),c=s(t,"value",12,""),k=s(t,"selectedText",12,""),l=s(t,"selectionStart",12,0),i=s(t,"selectionEnd",12,0),N=s(t,"saveFunction",8),Q=s(t,"debounceValue",8,2500),d=F(!1),u=F();const v=async()=>{try{N()(),h(d,!0),clearTimeout(y),y=setTimeout(()=>{h(d,!1)},1500)}catch(e){h(u,e instanceof Error?e.message:String(e))}};function m(){a()&&(l(a().selectionStart),i(a().selectionEnd),l()!==i()?k(c().substring(l(),i())):k(""))}const X=oe.debounce(v,Q());U(()=>{v()}),W();var w=ce(),z=ee(w),x=o(z),Y=o(x);g(Y,t,"header",{},null);var E=r(x,2);g(E,t,"default",{},null);var Z=r(E,2),_=o(Z);g(_,t,"title",{},null);var j=r(_,2);ne(j,te({get variant(){return C()},get size(){return J()},get color(){return L()},get resize(){return M()},placeholder:"Enter markdown content..."},()=>B,{get textInput(){return a()},set textInput(e){a(e)},get value(){return c()},set value(e){c(e)},$$events:{select:m,mouseup:m,keyup:()=>{m()},input:X,keydown:e=>{(e.key==="Escape"||(e.metaKey||e.ctrlKey)&&e.key==="s")&&(e.preventDefault(),v())}},$$legacy:!0}));var q=r(z,2),b=o(q),G=e=>{K(e,{size:1,weight:"light",color:"error",children:(p,P)=>{var n=D();ae(()=>re(n,$(u))),f(p,n)},$$slots:{default:!0}})};S(b,e=>{$(u)&&e(G)});var H=r(b,2),O=e=>{K(e,{size:1,weight:"light",color:"success",children:(p,P)=>{var n=D("Saved");f(p,n)},$$slots:{default:!0}})};S(H,e=>{$(d)&&e(O)}),f(V,w),se()}export{me as M};
