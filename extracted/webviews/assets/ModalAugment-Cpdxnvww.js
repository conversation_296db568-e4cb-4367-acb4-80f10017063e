import{z as oa,A as la,O as ta,B as t,D as ra,H as z,I as B,J as n,b as r,N as da,F as m,P as ia,G as y,t as v,Q as K,K as q,R as L,S as x,T as M,V as ca,W as na,X as va,Y as fa,Z as wa}from"./SpinnerAugment-DwpTQqCj.js";import{b as N,a as ha}from"./IconButtonAugment-CpHf993c.js";import{C as ma,s as O}from"./CardAugment-C6Oa90En.js";import{t as pa}from"./CollapseButtonAugment-CLwuB2Wy.js";var ua=m('<div class="c-modal-header svelte-1hwqfwo"><!></div>'),ba=m('<div class="c-modal-body svelte-1hwqfwo"><!></div>'),ka=m('<div class="c-modal-footer svelte-1hwqfwo"><!></div>'),ya=m('<div class="c-modal-content svelte-1hwqfwo"><!> <!> <!></div>'),qa=m('<div class="c-modal-backdrop svelte-1hwqfwo" role="presentation"><div class="c-modal svelte-1hwqfwo" role="dialog" aria-modal="true" tabindex="0"><!></div></div>');function Ba(P,a){const f=oa(a);la(a,!1);const p=ta();let D=t(a,"show",8,!1),u=t(a,"title",8,""),Q=t(a,"maxWidth",8,"400px"),R=t(a,"preventBackdropClose",8,!1),S=t(a,"preventEscapeClose",8,!1),E=t(a,"ariaLabelledBy",8,"modal-title"),A=t(a,"oncancel",24,()=>{}),T=t(a,"onbackdropClick",24,()=>{}),V=t(a,"onkeydown",24,()=>{});function X(){var e,o;R()||(p("cancel"),(e=A())==null||e()),p("backdropClick"),(o=T())==null||o()}function Y(e){var o,l;e.key!=="Escape"||S()||(e.preventDefault(),p("cancel"),(o=A())==null||o()),p("keydown",e),(l=V())==null||l(e)}ra();var F=z(),j=B(F),Z=e=>{var o=qa(),l=v(o),U=v(l);ma(U,{variant:"soft",size:3,children:(c,G)=>{var w=ya(),H=v(w),_=s=>{var d=ua(),h=v(d),C=i=>{var k=z(),$=B(k);q($,a,"header",{},null),r(i,k)},b=(i,k)=>{var $=g=>{va(g,{get id(){return E()},size:3,weight:"bold",color:"primary",children:(sa,xa)=>{var J=fa();M(()=>wa(J,u())),r(sa,J)},$$slots:{default:!0}})};n(i,g=>{u()&&g($)},k)};n(h,i=>{y(()=>f.header)?i(C):i(b,!1)}),r(s,d)};n(H,s=>{ia(u()),y(()=>u()||f.header)&&s(_)});var I=K(H,2),W=s=>{var d=ba(),h=v(d);q(h,a,"body",{},C=>{var b=z(),i=B(b);q(i,a,"default",{},null),r(C,b)}),r(s,d)};n(I,s=>{y(()=>f.body||f.default)&&s(W)});var aa=K(I,2),ea=s=>{var d=ka(),h=v(d);q(h,a,"footer",{},null),r(s,d)};n(aa,s=>{y(()=>f.footer)&&s(ea)}),r(c,w)},$$slots:{default:!0}}),L(()=>x("click",l,O(function(c){N.call(this,a,c)}))),L(()=>x("keydown",l,O(function(c){N.call(this,a,c)}))),ha(l,(c,G)=>{var w;return(w=pa)==null?void 0:w(c,G)},()=>({enabled:D()})),M(()=>{ca(l,"aria-labelledby",E()),na(l,`max-width: ${Q()??""}`)}),x("click",o,X),x("keydown",o,Y),r(e,o)};n(j,e=>{D()&&e(Z)}),r(P,F),da()}export{Ba as M};
