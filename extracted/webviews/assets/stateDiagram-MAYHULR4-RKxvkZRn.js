import{s as O,a as E,b as R}from"./chunk-7U56Z5CX-BA7fLxaE.js";import{_ as m,c as t,d as T,l as S,e as P,k as z,a1 as U,a8 as C,u as I}from"./AugmentMessage-DpP2I4-b.js";import{G as W}from"./graph-DOYS9EEb.js";import{l as j}from"./layout-ano9UcMP.js";import"./chunk-5HRBRIJM-DwA_usAX.js";import"./SpinnerAugment-DwpTQqCj.js";import"./CalloutAugment-BhO9fdsc.js";import"./CardAugment-C6Oa90En.js";import"./IconButtonAugment-CpHf993c.js";import"./index-CF0COlDh.js";import"./async-messaging-3euWO_Vj.js";import"./message-broker-CJi4HhV6.js";import"./types-CGlLNakm.js";import"./focusTrapStack-CKc3PkF0.js";import"./isObjectLike-BdluLIvS.js";import"./BaseTextInput-D9ZtAsmP.js";import"./index-BddLnVwz.js";import"./diff-operations-jldzuAhI.js";import"./svelte-component-OFA3-Jvi.js";import"./Filespan-CZ9Yk5fv.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BxJ3Yi_l.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-D7I-QOBo.js";import"./await-SODcoE9a.js";import"./OpenFileButton-rvNFtINp.js";import"./chat-model-context-52iR26yw.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-DIrnFfVT.js";import"./ra-diff-ops-model-CrGPikPJ.js";import"./TextAreaAugment-o5eN7Hov.js";import"./ButtonAugment-UYn0l1qJ.js";import"./CollapseButtonAugment-CLwuB2Wy.js";import"./user-DJbhSuGe.js";import"./MaterialIcon-BspaVi72.js";import"./CopyButton-BsD73rhm.js";import"./copy-DHWxo32G.js";import"./ellipsis-mzD6NhIP.js";import"./LanguageIcon-Dnvdydi_.js";import"./chevron-down-CifcRB5W.js";import"./index-McJAeurX.js";import"./augment-logo-8QzK3Xj7.js";import"./pen-to-square-DcsW-Net.js";import"./check-8SJSJBbK.js";import"./_baseUniq-Br3yl8DR.js";import"./_basePickBy-DLTCDoex.js";var w,H={},F=m((e,i)=>{H[e]=i},"set"),J=m(e=>H[e],"get"),G=m(()=>Object.keys(H),"keys"),Y=m(()=>G().length,"size"),$={get:J,set:F,keys:G,size:Y},X=m(e=>e.append("circle").attr("class","start-state").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit).attr("cy",t().state.padding+t().state.sizeUnit),"drawStartState"),_=m(e=>e.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",t().state.textHeight).attr("class","divider").attr("x2",2*t().state.textHeight).attr("y1",0).attr("y2",0),"drawDivider"),q=m((e,i)=>{const o=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+2*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),d=o.node().getBBox();return e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",d.width+2*t().state.padding).attr("height",d.height+2*t().state.padding).attr("rx",t().state.radius),o},"drawSimpleState"),Z=m((e,i)=>{const o=m(function(p,u,b){const B=p.append("tspan").attr("x",2*t().state.padding).text(u);b||B.attr("dy",t().state.textHeight)},"addTspan"),d=e.append("text").attr("x",2*t().state.padding).attr("y",t().state.textHeight+1.3*t().state.padding).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.descriptions[0]).node().getBBox(),n=d.height,h=e.append("text").attr("x",t().state.padding).attr("y",n+.4*t().state.padding+t().state.dividerMargin+t().state.textHeight).attr("class","state-description");let l=!0,a=!0;i.descriptions.forEach(function(p){l||(o(h,p,a),a=!1),l=!1});const s=e.append("line").attr("x1",t().state.padding).attr("y1",t().state.padding+n+t().state.dividerMargin/2).attr("y2",t().state.padding+n+t().state.dividerMargin/2).attr("class","descr-divider"),x=h.node().getBBox(),c=Math.max(x.width,d.width);return s.attr("x2",c+3*t().state.padding),e.insert("rect",":first-child").attr("x",t().state.padding).attr("y",t().state.padding).attr("width",c+2*t().state.padding).attr("height",x.height+n+2*t().state.padding).attr("rx",t().state.radius),e},"drawDescrState"),K=m((e,i,o)=>{const d=t().state.padding,n=2*t().state.padding,h=e.node().getBBox(),l=h.width,a=h.x,s=e.append("text").attr("x",0).attr("y",t().state.titleShift).attr("font-size",t().state.fontSize).attr("class","state-title").text(i.id),x=s.node().getBBox().width+n;let c,p=Math.max(x,l);p===l&&(p+=n);const u=e.node().getBBox();i.doc,c=a-d,x>l&&(c=(l-p)/2+d),Math.abs(a-u.x)<d&&x>l&&(c=a-(x-l)/2);const b=1-t().state.textHeight;return e.insert("rect",":first-child").attr("x",c).attr("y",b).attr("class",o?"alt-composit":"composit").attr("width",p).attr("height",u.height+t().state.textHeight+t().state.titleShift+1).attr("rx","0"),s.attr("x",c+d),x<=l&&s.attr("x",a+(p-n)/2-x/2+d),e.insert("rect",":first-child").attr("x",c).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",p).attr("height",3*t().state.textHeight).attr("rx",t().state.radius),e.insert("rect",":first-child").attr("x",c).attr("y",t().state.titleShift-t().state.textHeight-t().state.padding).attr("width",p).attr("height",u.height+3+2*t().state.textHeight).attr("rx",t().state.radius),e},"addTitleAndBox"),Q=m(e=>(e.append("circle").attr("class","end-state-outer").attr("r",t().state.sizeUnit+t().state.miniPadding).attr("cx",t().state.padding+t().state.sizeUnit+t().state.miniPadding).attr("cy",t().state.padding+t().state.sizeUnit+t().state.miniPadding),e.append("circle").attr("class","end-state-inner").attr("r",t().state.sizeUnit).attr("cx",t().state.padding+t().state.sizeUnit+2).attr("cy",t().state.padding+t().state.sizeUnit+2)),"drawEndState"),V=m((e,i)=>{let o=t().state.forkWidth,d=t().state.forkHeight;if(i.parentId){let n=o;o=d,d=n}return e.append("rect").style("stroke","black").style("fill","black").attr("width",o).attr("height",d).attr("x",t().state.padding).attr("y",t().state.padding)},"drawForkJoinState"),tt=m((e,i,o,d)=>{let n=0;const h=d.append("text");h.style("text-anchor","start"),h.attr("class","noteText");let l=e.replace(/\r\n/g,"<br/>");l=l.replace(/\n/g,"<br/>");const a=l.split(z.lineBreakRegex);let s=1.25*t().state.noteMargin;for(const x of a){const c=x.trim();if(c.length>0){const p=h.append("tspan");p.text(c),s===0&&(s+=p.node().getBBox().height),n+=s,p.attr("x",i+t().state.noteMargin),p.attr("y",o+n+1.25*t().state.noteMargin)}}return{textWidth:h.node().getBBox().width,textHeight:n}},"_drawLongText"),et=m((e,i)=>{i.attr("class","state-note");const o=i.append("rect").attr("x",0).attr("y",t().state.padding),d=i.append("g"),{textWidth:n,textHeight:h}=tt(e,0,0,d);return o.attr("height",h+2*t().state.noteMargin),o.attr("width",n+2*t().state.noteMargin),o},"drawNote"),D=m(function(e,i){const o=i.id,d={id:o,label:i.id,width:0,height:0},n=e.append("g").attr("id",o).attr("class","stateGroup");i.type==="start"&&X(n),i.type==="end"&&Q(n),i.type!=="fork"&&i.type!=="join"||V(n,i),i.type==="note"&&et(i.note.text,n),i.type==="divider"&&_(n),i.type==="default"&&i.descriptions.length===0&&q(n,i),i.type==="default"&&i.descriptions.length>0&&Z(n,i);const h=n.node().getBBox();return d.width=h.width+2*t().state.padding,d.height=h.height+2*t().state.padding,$.set(o,d),d},"drawState"),L=0,at=m(function(e,i,o){const d=m(function(s){switch(s){case E.relationType.AGGREGATION:return"aggregation";case E.relationType.EXTENSION:return"extension";case E.relationType.COMPOSITION:return"composition";case E.relationType.DEPENDENCY:return"dependency"}},"getRelationType");i.points=i.points.filter(s=>!Number.isNaN(s.y));const n=i.points,h=U().x(function(s){return s.x}).y(function(s){return s.y}).curve(C),l=e.append("path").attr("d",h(n)).attr("id","edge"+L).attr("class","transition");let a="";if(t().state.arrowMarkerAbsolute&&(a=window.location.protocol+"//"+window.location.host+window.location.pathname+window.location.search,a=a.replace(/\(/g,"\\("),a=a.replace(/\)/g,"\\)")),l.attr("marker-end","url("+a+"#"+d(E.relationType.DEPENDENCY)+"End)"),o.title!==void 0){const s=e.append("g").attr("class","stateLabel"),{x,y:c}=I.calcLabelPosition(i.points),p=z.getRows(o.title);let u=0;const b=[];let B=0,M=0;for(let f=0;f<=p.length;f++){const g=s.append("text").attr("text-anchor","middle").text(p[f]).attr("x",x).attr("y",c+u),y=g.node().getBBox();B=Math.max(B,y.width),M=Math.min(M,y.x),S.info(y.x,x,c+u),u===0&&(u=g.node().getBBox().height,S.info("Title height",u,c)),b.push(g)}let N=u*p.length;if(p.length>1){const f=(p.length-1)*u*.5;b.forEach((g,y)=>g.attr("y",c+y*u-f)),N=u*p.length}const r=s.node().getBBox();s.insert("rect",":first-child").attr("class","box").attr("x",x-B/2-t().state.padding/2).attr("y",c-N/2-t().state.padding/2-3.5).attr("width",B+t().state.padding).attr("height",N+t().state.padding),S.info(r)}L++},"drawEdge"),v={},it=m(function(){},"setConf"),rt=m(function(e){e.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"insertMarkers"),nt=m(function(e,i,o,d){w=t().state;const n=t().securityLevel;let h;n==="sandbox"&&(h=T("#i"+i));const l=T(n==="sandbox"?h.nodes()[0].contentDocument.body:"body"),a=n==="sandbox"?h.nodes()[0].contentDocument:document;S.debug("Rendering diagram "+e);const s=l.select(`[id='${i}']`);rt(s);const x=d.db.getRootDoc();A(x,s,void 0,!1,l,a,d);const c=w.padding,p=s.node().getBBox(),u=p.width+2*c,b=p.height+2*c;P(s,b,1.75*u,w.useMaxWidth),s.attr("viewBox",`${p.x-w.padding}  ${p.y-w.padding} `+u+" "+b)},"draw"),dt=m(e=>e?e.length*w.fontSizeFactor:1,"getLabelWidth"),A=m((e,i,o,d,n,h,l)=>{const a=new W({compound:!0,multigraph:!0});let s,x=!0;for(s=0;s<e.length;s++)if(e[s].stmt==="relation"){x=!1;break}o?a.setGraph({rankdir:"LR",multigraph:!0,compound:!0,ranker:"tight-tree",ranksep:x?1:w.edgeLengthFactor,nodeSep:x?1:50,isMultiGraph:!0}):a.setGraph({rankdir:"TB",multigraph:!0,compound:!0,ranksep:x?1:w.edgeLengthFactor,nodeSep:x?1:50,ranker:"tight-tree",isMultiGraph:!0}),a.setDefaultEdgeLabel(function(){return{}}),l.db.extract(e);const c=l.db.getStates(),p=l.db.getRelations(),u=Object.keys(c);for(const r of u){const f=c[r];let g;if(o&&(f.parentId=o),f.doc){let y=i.append("g").attr("id",f.id).attr("class","stateGroup");g=A(f.doc,y,f.id,!d,n,h,l);{y=K(y,f,d);let k=y.node().getBBox();g.width=k.width,g.height=k.height+w.padding/2,v[f.id]={y:w.compositTitleSize}}}else g=D(i,f,a);if(f.note){const y={descriptions:[],id:f.id+"-note",note:f.note,type:"note"},k=D(i,y,a);f.note.position==="left of"?(a.setNode(g.id+"-note",k),a.setNode(g.id,g)):(a.setNode(g.id,g),a.setNode(g.id+"-note",k)),a.setParent(g.id,g.id+"-group"),a.setParent(g.id+"-note",g.id+"-group")}else a.setNode(g.id,g)}S.debug("Count=",a.nodeCount(),a);let b=0;p.forEach(function(r){b++,S.debug("Setting edge",r),a.setEdge(r.id1,r.id2,{relation:r,width:dt(r.title),height:w.labelHeight*z.getRows(r.title).length,labelpos:"c"},"id"+b)}),j(a),S.debug("Graph after layout",a.nodes());const B=i.node();a.nodes().forEach(function(r){r!==void 0&&a.node(r)!==void 0?(S.warn("Node "+r+": "+JSON.stringify(a.node(r))),n.select("#"+B.id+" #"+r).attr("transform","translate("+(a.node(r).x-a.node(r).width/2)+","+(a.node(r).y+(v[r]?v[r].y:0)-a.node(r).height/2)+" )"),n.select("#"+B.id+" #"+r).attr("data-x-shift",a.node(r).x-a.node(r).width/2),h.querySelectorAll("#"+B.id+" #"+r+" .divider").forEach(f=>{const g=f.parentElement;let y=0,k=0;g&&(g.parentElement&&(y=g.parentElement.getBBox().width),k=parseInt(g.getAttribute("data-x-shift"),10),Number.isNaN(k)&&(k=0)),f.setAttribute("x1",0-k+8),f.setAttribute("x2",y-k-8)})):S.debug("No Node "+r+": "+JSON.stringify(a.node(r)))});let M=B.getBBox();a.edges().forEach(function(r){r!==void 0&&a.edge(r)!==void 0&&(S.debug("Edge "+r.v+" -> "+r.w+": "+JSON.stringify(a.edge(r))),at(i,a.edge(r),a.edge(r).relation))}),M=B.getBBox();const N={id:o||"root",label:o||"root",width:0,height:0};return N.width=M.width+2*w.padding,N.height=M.height+2*w.padding,S.debug("Doc rendered",N,a),N},"renderDoc"),te={parser:R,db:E,renderer:{setConf:it,draw:nt},styles:O,init:m(e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute,E.clear()},"init")};export{te as diagram};
