import{c as U,E as V,n as W,p as Y,r as aa,ac as sa,ad as na,ae as ea,af as la,ag as ta,ah as B,z as ra,A as ca,B as f,_ as d,a0 as ia,D as oa,X as pa,H as D,I as E,b as u,N as va,a as fa,S as ua,F as k,J as S,G,Q as $,T as H,Z as J,L as i,m as h,t as m,K,M as da,ab as ha,C as g,P as L}from"./SpinnerAugment-DwpTQqCj.js";import{n as ma,g as ga,a as _a}from"./focusTrapStack-CKc3PkF0.js";function ba(_,s,b,r,I,z){var o,p,l,t=null,x=_;U(()=>{const e=s()||null;var c=e==="svg"?na:null;e!==o&&(l&&(e===null?Y(l,()=>{l=null,p=null}):e===p?aa(l):(sa(l),B(!1))),e&&e!==p&&(l=W(()=>{if(t=c?document.createElementNS(c,e):document.createElement(e),ea(t,t),r){var C=t.appendChild(la());r(t,C)}ta.nodes_end=t,x.before(t)})),(o=e)&&(p=o),B(!0))},V)}var Ia=k('<div><div class="c-filespan__dir-text svelte-9pfhnp"> </div></div>'),za=k('<span class="right-icons svelte-9pfhnp"><!></span>'),xa=k('<!> <span class="c-filespan__filename svelte-9pfhnp"> </span> <!> <!>',1);function $a(_,s){const b=ra(s);ca(s,!1);const r=h(),I=h(),z=h(),o=h();let p=f(s,"class",8,""),l=f(s,"filepath",8),t=f(s,"size",8,1),x=f(s,"nopath",8,!1),e=f(s,"growname",8,!0),c=f(s,"onClick",24,()=>{});d(()=>L(l()),()=>{g(r,ma(l()))}),d(()=>i(r),()=>{g(I,ga(i(r)))}),d(()=>i(r),()=>{g(z,_a(i(r)))}),d(()=>L(c()),()=>{g(o,c()?"button":"div")}),ia(),oa(),pa(_,{get size(){return t()},children:(C,Ca)=>{var w=D();ba(E(w),()=>i(o),0,(F,M)=>{fa(F,()=>({class:`c-filespan ${p()}`,role:c()?"button":"",tabindex:"0"}),void 0,"svelte-9pfhnp"),ua("click",F,function(...a){var n;(n=c())==null||n.apply(this,a)});var N=xa(),X=E(N),P=a=>{var n=D(),v=E(n);K(v,s,"leftIcon",{},null),u(a,n)};S(X,a=>{G(()=>b.leftIcon)&&a(P)});var y=$(X,2),Q=m(y),A=$(y,2),T=a=>{var n=Ia();let v;var q=m(n),O=m(q);H(R=>{v=ha(n,1,"c-filespan__dir svelte-9pfhnp",null,v,R),J(O,i(z))},[()=>({growname:e()})],da),u(a,n)};S(A,a=>{x()||a(T)});var j=$(A,2),Z=a=>{var n=za(),v=m(n);K(v,s,"rightIcon",{},null),u(a,n)};S(j,a=>{G(()=>b.rightIcon)&&a(Z)}),H(()=>J(Q,i(I))),u(M,N)}),u(C,w)},$$slots:{default:!0}}),va()}export{$a as F,ba as e};
