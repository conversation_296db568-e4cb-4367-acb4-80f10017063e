<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Rules Editor</title>
    <meta property="csp-nonce" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <script type="module" crossorigin src="./assets/rules-CxZMThVY.js" nonce="nonce-T2f04YlLaif0pP94p5d40w=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-DwpTQqCj.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-HUS0Ln1X.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CpHf993c.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-UYn0l1qJ.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-C6Oa90En.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-52iR26yw.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-3euWO_Vj.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-CJi4HhV6.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CKc3PkF0.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BdluLIvS.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-D9ZtAsmP.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-CF0COlDh.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-BddLnVwz.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-DIrnFfVT.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-CrGPikPJ.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-o5eN7Hov.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-rvNFtINp.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-CifcRB5W.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/MarkdownEditor-BbzKUhG_.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-CWzijNQK.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-left-DiP869pR.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/MarkdownEditor-B6vv3aGc.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BtrmW4jo.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/rules-D-OA6lpr.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
