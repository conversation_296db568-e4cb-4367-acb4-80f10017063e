<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment</title>
    <script nonce="nonce-T2f04YlLaif0pP94p5d40w==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <script type="module" crossorigin src="./assets/main-panel-Bovq8V-X.js" nonce="nonce-T2f04YlLaif0pP94p5d40w=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-DwpTQqCj.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-HUS0Ln1X.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-CpHf993c.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-3euWO_Vj.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-CJi4HhV6.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-BhO9fdsc.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-C6Oa90En.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/focusTrapStack-CKc3PkF0.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/isObjectLike-BdluLIvS.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-D9ZtAsmP.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-CF0COlDh.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-BddLnVwz.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/svelte-component-OFA3-Jvi.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-CZ9Yk5fv.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-BxJ3Yi_l.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-jldzuAhI.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-D7I-QOBo.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/await-SODcoE9a.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/chat-model-context-52iR26yw.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-C4gKbsWy.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-DIrnFfVT.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-CrGPikPJ.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-UYn0l1qJ.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-o5eN7Hov.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-rvNFtINp.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-CLwuB2Wy.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/user-DJbhSuGe.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-BspaVi72.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/copy-DHWxo32G.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-BsD73rhm.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-mzD6NhIP.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-Dnvdydi_.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-CifcRB5W.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/index-McJAeurX.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-8QzK3Xj7.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-DcsW-Net.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/check-8SJSJBbK.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-DpP2I4-b.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-CmJXetvn.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/download-Cu_T_p5v.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-BcOah4Yh.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/Keybindings-7lHDA6-t.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-Cpdxnvww.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-CsjyscKa.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-CYyXFtBj.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/differenceInCalendarDays-CwIWhZCr.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-left-DiP869pR.js" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DoxdFmoV.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B4afvB2A.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-C-z-uXWx.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BtrmW4jo.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-CEzLOEg8.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/index-D9au8v71.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMEPAZfs.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/index-BlHvDt2c.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-MyOXHVsl.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-DORgvEFm.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-k8sG2hbx.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-bH4F3VXH.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-B1EFpTQV.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/user-dXUx3CYB.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-B0_wR17F.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-CgXbz9Sy.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-DCeibtmX.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/download-C1VayJBB.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/Keybindings-CiyHMwmH.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-BVaLv7mP.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
    <link rel="stylesheet" crossorigin href="./assets/main-panel-B03Ph_YA.css" nonce="nonce-T2f04YlLaif0pP94p5d40w==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
